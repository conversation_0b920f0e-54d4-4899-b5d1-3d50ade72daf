class WebSocketManager {
  constructor(url, exchange, options = {}) {
    this.url = url;
    this.exchange = exchange;
    this.name = options.name || exchange;
    this.connected = false;
    this.connecting = false;
    this.reconnectDelay = options.reconnectDelay || 2000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.pingInterval = options.pingInterval || 30000;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.subscriptions = new Map(); // channel -> true or metadata
    this.pendingSubscriptions = new Set();
    this.handlers = new Map(); // channel -> [handlers]
    this.pingTimer = null;
    this.lastPongTime = 0;
    this.intentionalClose = false;
    this.connectionTimeout = null;
    this.messageCallback = null;
    this.networkStatus = navigator.onLine;

    // Track last message received time for freeze detection (Bitstamp)
    this.lastMessageTimestamp = Date.now();

    // DEBUG flag for easy removal of debug logs
    this.DEBUG = false;

    // Throttle log messages to avoid console spam
    this.throttledLog = commonUtils.throttle(console.log, 1000);

    // Bind methods that will be used as callbacks
    this._handleNetworkStatusChange =
      this._handleNetworkStatusChange.bind(this);
    this._handleVisibilityChange = this._handleVisibilityChange.bind(this);
    this._handleConnectionOpen = this._handleConnectionOpen.bind(this);
    this._handleMessage = this._handleMessage.bind(this);

    // Track last time the tab was hidden
    this.lastHiddenTime = null;
    // this.lightCleanupTimeout = null; // Removed as performGlobalLightCleanup is no longer used
    // this.optimizerReady = false; // Removed as performanceOptimizer is no longer used

    // Add event listeners for online/offline events
    window.addEventListener("online", () =>
      this._handleNetworkStatusChange(true),
    );
    window.addEventListener("offline", () =>
      this._handleNetworkStatusChange(false),
    );

    // Add event listener for visibility change (tab focus/blur, sleep/wake)
    document.addEventListener("visibilitychange", () =>
      this._handleVisibilityChange(),
    );

    // Debounce for visibility reconnects
    this._visibilityReconnectDebounce = false;

    // Listen for performanceOptimizer readiness - REMOVED as performanceOptimizer.js is removed
    // document.addEventListener(
    //   "performanceOptimizerReady",
    //   () => {
    //     console.log(
    //       `[WSManager-${this.exchange}] Received performanceOptimizerReady event.`,
    //     );
    //     this.optimizerReady = true;
    //   },
    //   { once: true },
    // );

    // Connect immediately
    this.connect();
  }

  // Add ping/pong mechanism
  startPingPong() {
    this.stopPingPong();
    this.lastPongTime = Date.now();

    this.pingTimer = setInterval(() => {
      if (!this.connected || !this.ws) {
        this.stopPingPong();
        return;
      }

      // Check if we've received a pong recently
      const now = Date.now();
      if (now - this.lastPongTime > this.pingInterval * 2) {
        this.reconnect(true);
        return;
      }

      // --- Bitstamp freeze detection ---
      if (this.exchange === "bitstamp") {
        // If no message received in 60 seconds, force reconnect
        if (now - this.lastMessageTimestamp > 60000) {
          this.reconnect(true);
          return;
        }
      }

      // Send ping based on exchange
      // Note: _sendPing() has its own error handling.
      // Bitstamp's specific logic (readyState check and lastPongTime update) remains here
      // as it's part of its unique keep-alive within the main ping timer.
      if (this.exchange === "bybit") {
        // For Bybit, we can directly call _sendPing or send here.
        // Calling _sendPing centralizes the actual send logic.
        this._sendPing();
      } else if (this.exchange === "bitstamp") {
        // Bitstamp doesn't support actual ping messages.
        // Check connection state directly.
        if (this.ws.readyState !== WebSocket.OPEN) {
          this.reconnect(true);
        } else {
          // If connection is open, just update pong time.
          // Actual message check (lastMessageTimestamp) is done above.
          this.lastPongTime = now;
        }
      }
      // For other exchanges that might be added and use a generic ping,
      // they would need to be handled or use _sendPing() if it defaults.
      // However, current _sendPing is mostly for Bybit or generic.
      // Consider if a direct this._sendPing() call is better for all non-Bitstamp cases.
      // For now, keeping Bybit explicit to match previous logic structure minus try-catch.
    }, this.pingInterval);
  }

  stopPingPong() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  connect() {
    // Only allow connect if not already connecting or connected and ws is null or closed
    if (
      this.connected ||
      this.connecting ||
      (this.ws &&
        this.ws.readyState !== WebSocket.CLOSED &&
        this.ws.readyState !== undefined)
    ) {
      return;
    }

    this.connecting = true;

    // If ws exists and is not closed, wait for onclose before connecting
    if (
      this.ws &&
      this.ws.readyState !== WebSocket.CLOSED &&
      this.ws.readyState !== undefined
    ) {
      this._pendingConnectAfterClose = true;
      this._cleanupExistingConnection();
      return;
    }

    try {
      // Check network status before attempting to connect
      if (!navigator.onLine) {
        this.connecting = false;
        setTimeout(() => this.reconnect(false), this.reconnectDelay);
        return;
      }

      // Create new WebSocket connection
      this.ws = new WebSocket(this.url);

      // Set binary type for better performance with binary messages
      if (this.exchange === "bybit") {
        this.ws.binaryType = "arraybuffer";
      }

      // Set connection timeout with adaptive timing based on previous attempts
      const baseTimeout = 10000;
      const maxTimeout = 30000;
      const timeout = Math.min(
        baseTimeout * (1 + this.reconnectAttempts * 0.5),
        maxTimeout,
      );

      this.connectionTimeout = setTimeout(() => {
        if (!this.connected) {
          this._cleanupExistingConnection();
          this.connecting = false;
          // Only reconnect if not already connecting (guard against race)
          if (!this.connecting) {
            this.reconnect();
          }
        }
      }, timeout);

      // Connection opened handler
      this.ws.onopen = this._handleConnectionOpen.bind(this);

      // Message handler with performance optimizations
      this.ws.onmessage = (event) => {
        // Update last message timestamp for freeze detection
        this.lastMessageTimestamp = Date.now();
        this._handleMessage(event);
      };

      // Error handler - log but let onclose handle reconnection
      this.ws.onerror = (error) => {
        this.reportError(error);
      };

      // Close handler with reconnection logic
      this.ws.onclose = (event) => {
        clearTimeout(this.connectionTimeout);
        this.connected = false;
        this.connecting = false;
        // Only set ws = null here for proper sequencing
        this.ws = null;
        // If a connect was queued, do it now
        if (this._pendingConnectAfterClose) {
          this._pendingConnectAfterClose = false;
          setTimeout(() => this.connect(), 10);
        } else if (!this.intentionalClose) {
          // Don't reconnect if we're intentionally closing
          if (!this.connecting && !this._pendingReconnect) {
            setTimeout(() => this.reconnect(), 100);
          }
        }
        this.intentionalClose = false;
      };
    } catch (error) {
      clearTimeout(this.connectionTimeout);
      this.connected = false;
      this.connecting = false;

      // Schedule reconnection with a small delay
      setTimeout(() => {
        if (!this.connecting && !this._pendingReconnect) {
          this.reconnect();
        }
      }, 500);
    }
  }

  reconnect(force = false) {
    // Prevent redundant/parallel reconnects
    if (this.connecting || this._pendingReconnect) {
      return;
    }
    // Debounce forced reconnects
    if (force) {
      const now = Date.now();
      if (now - this._lastForcedReconnect < 500) {
        return;
      }
      this._lastForcedReconnect = now;
    }
    this._pendingReconnect = true;
    // Don't attempt reconnection if we're offline
    if (!navigator.onLine) {
      this._pendingReconnect = false;
      return;
    }

    this.stopPingPong();

    // Clean up existing connection
    if (
      this.ws &&
      this.ws.readyState !== WebSocket.CLOSED &&
      this.ws.readyState !== undefined
    ) {
      try {
        // Only attempt to close if the connection is still open or connecting
        if (
          this.ws.readyState === WebSocket.OPEN ||
          this.ws.readyState === WebSocket.CONNECTING
        ) {
          this._pendingConnectAfterClose = true;
          this.ws.close();
          // Wait for onclose to trigger connect
          this._pendingReconnect = false;
          return;
        }
      } catch (e) {
        // Ignore errors when closing
      }
      // ws will be set to null in onclose
    }

    if (force) {
      this.reconnectAttempts = 0;
      this.connected = false; // Explicitly set
      this.connecting = false; // Explicitly set
      this._pendingReconnect = false;
      this.connect();
      return;
    }

    // Check if we've reached max attempts
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      const longDelay = this.reconnectDelay * 10;
      // Reset reconnect attempts after a longer timeout to try again later
      setTimeout(() => {
        this.reconnectAttempts = 0;
        if (navigator.onLine) this.connect();
      }, longDelay);
      this._pendingReconnect = false;
      return;
    }

    // Calculate exponential backoff with a maximum limit
    const maxBackoffMultiplier = 10;
    const backoffMultiplier = Math.min(
      Math.pow(1.5, this.reconnectAttempts),
      maxBackoffMultiplier,
    );
    const delay = this.reconnectDelay * backoffMultiplier;

    this.reconnectAttempts++;

    // Log reconnection attempt
    console.log(
      `[WSManager-${this.exchange}] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
    );

    // Schedule reconnection
    setTimeout(() => {
      // Double-check online status before attempting to connect
      if (navigator.onLine) {
        if (!this.connecting && !this._pendingReconnect) {
          this.connected = false; // Explicitly set
          this.connecting = false; // Explicitly set
          this.connect();
        }
      } else {
        // Try again later when potentially back online
        setTimeout(() => this.reconnect(false), this.reconnectDelay);
      }
      this._pendingReconnect = false;
    }, delay);
  }

  processMessage(data) {
    // Find all handlers that should receive this message
    for (const [channel, handlers] of this.handlers.entries()) {
      // For Bitstamp
      if (this.exchange === "bitstamp" && data.channel === channel) {
        handlers.forEach((handler) => handler(data));
        return;
      }

      // For Bybit
      if (this.exchange === "bybit" && data.topic === channel) {
        handlers.forEach((handler) => handler(data));
        return;
      }
    }
  }

  subscribe(channel, handler) {
    // Initialize handlers array for this channel if it doesn't exist
    let isNew = false;
    if (!this.handlers.has(channel)) {
      this.handlers.set(channel, []);
      isNew = true;
    }
    this.handlers.get(channel).push(handler);

    // Only add to subscriptions if this is a new channel
    if (isNew) {
      this.subscriptions.set(channel, true);
      if (this.connected) {
        this.sendSubscription([channel]);
      } else {
        this.pendingSubscriptions.add(channel);
      }
    }

    // Debug log for subscribe
    if (this.DEBUG && this.exchange === "bitstamp") {
      console.debug(
        `[WS-DEBUG] [${this.exchange}] subscribe() called for channel:`,
        channel,
        "at",
        new Date().toISOString(),
      );
    }

    return this;
  }

  unsubscribe(channel, handler) {
    if (!this.handlers.has(channel)) return;

    if (this.DEBUG && this.exchange === "bitstamp") {
      console.debug(
        `[WS-DEBUG] [${this.exchange}] unsubscribe() called for channel:`,
        channel,
        "at",
        new Date().toISOString(),
      );
    }

    if (handler) {
      // Remove specific handler
      const arr = this.handlers.get(channel).filter((h) => h !== handler);

      if (arr.length === 0) {
        // No handlers left, fully unsubscribe
        this.sendUnsubscription([channel]);
        this.handlers.delete(channel);
        this.subscriptions.delete(channel);
        this.pendingSubscriptions.delete(channel);
      } else {
        this.handlers.set(channel, arr);
      }
    } else {
      // Remove all handlers for this channel
      this.sendUnsubscription([channel]);
      this.handlers.delete(channel);
      this.subscriptions.delete(channel);
      this.pendingSubscriptions.delete(channel);
    }
  }

  sendSubscription(channels) {
    if (!this.connected || !this.ws) return;
    // If no channels provided, subscribe to all current channels
    if (!channels || (Array.isArray(channels) && channels.length === 0)) {
      channels = Array.from(this.subscriptions.keys());
    }
    if (!Array.isArray(channels)) channels = [channels];

    try {
      let msgSent = false;
      if (this.exchange === "bitstamp") {
        for (const channel of channels) {
          const msg = this._createBitstampSubscribeMessage(channel);
          this.ws.send(JSON.stringify(msg));
          if (this.DEBUG) {
            this.throttledLog(
              "debug",
              `[WSManager-${this.exchange}] Sent SUBSCRIBE request for channel: ${channel}`,
            );
          }
          msgSent = true;
        }
      } else if (this.exchange === "bybit") {
        const msg = this._createBybitSubscribeMessage(channels);
        this.ws.send(JSON.stringify(msg));
        if (this.DEBUG) {
          this.throttledLog(
            "debug",
            `[WSManager-${this.exchange}] Sent SUBSCRIBE request for channels: ${JSON.stringify(msg.args)}`,
          );
        }
        msgSent = true;
      }
      // General log if not in DEBUG and message was sent (optional, can be noisy)
      // if (msgSent && !this.DEBUG) {
      //    this.throttledLog('info', `[WSManager-${this.exchange}] Sent subscription request for ${channels.length} channel(s).`);
      // }
    } catch (error) {
      this.throttledLog(
        "error",
        `[WSManager-${this.exchange}] Error subscribing to ${channels}: ${error.message}`,
      );
    }
  }

  sendUnsubscription(channels) {
    if (!this.connected || !this.ws) return;
    // If no channels provided, unsubscribe from all current channels
    if (!channels || (Array.isArray(channels) && channels.length === 0)) {
      channels = Array.from(this.subscriptions.keys());
    }
    if (!Array.isArray(channels)) channels = [channels];

    try {
      if (this.exchange === "bitstamp") {
        for (const channel of channels) {
          const msg = this._createBitstampUnsubscribeMessage(channel);
          if (this.DEBUG) {
            console.debug(
              `[WS-DEBUG] [${this.exchange}] Sending UNSUBSCRIBE:`,
              msg,
              "at",
              new Date().toISOString(),
            );
          }
          this.ws.send(JSON.stringify(msg));
        }
      } else if (this.exchange === "bybit") {
        const msg = this._createBybitUnsubscribeMessage(channels);
        if (this.DEBUG) {
          console.debug(
            `[WS-DEBUG] [${this.exchange}] Sending UNSUBSCRIBE:`,
            msg,
            "at",
            new Date().toISOString(),
          );
        }
        this.ws.send(JSON.stringify(msg));
      }
      this.throttledLog(
        "unsubscribe",
        `Unsubscribed from ${this.exchange} channel(s): ${channels}`,
      );
    } catch (error) {
      this.throttledLog(
        "error",
        `Error unsubscribing from ${channels}: ${error}`,
      );
      this.reportError(error);
    }
  }

  resubscribeAll() {
    // Clear pending subscriptions
    this.pendingSubscriptions.clear();
    // Resubscribe to all channels in batch
    const channels = Array.from(this.subscriptions.keys()).filter(Boolean);
    if (channels.length > 0) {
      this.sendSubscription(channels);
    }
  }

  close() {
    this.intentionalClose = true;
    this._cleanupExistingConnection();
    this.connected = false;
    this.connecting = false;
  }

  // Add isConnected method for external status checking
  isConnected() {
    return (
      this.connected &&
      this.ws &&
      this.ws.readyState === WebSocket.OPEN &&
      navigator.onLine
    );
  }

  // Report errors to the error manager
  reportError(error, options = {}) {
    // Use ErrorManager if available
    if (window.ErrorManager) {
      return window.ErrorManager.reportError("websocket", error, {
        exchange: this.exchange,
        name: this.name,
        ...options,
      });
    } else {
      // Fallback to console
      console.error(`WebSocket error (${this.exchange}):`, error);
      return null;
    }
  }

  // Get connection status information
  getStatus() {
    return {
      exchange: this.exchange,
      connected: this.connected,
      connecting: this.connecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions),
      pendingSubscriptions: Array.from(this.pendingSubscriptions),
      lastPongTime: this.lastPongTime,
      lastMessageTime: this.lastMessageTime || 0,
    };
  }

  // Debug method to log current state
  // debug() method removed as unused.

  // Helper methods for cleaner code organization
  _cleanupExistingConnection() {
    if (this.ws) {
      this.intentionalClose = true;
      // Only attempt to close if the connection is not already closing or closed.
      // WebSocket.CONNECTING = 0, WebSocket.OPEN = 1, WebSocket.CLOSING = 2, WebSocket.CLOSED = 3
      if (this.ws.readyState < WebSocket.CLOSING) {
        // Check if CONNECTING (0) or OPEN (1)
        try {
          this.ws.close();
        } catch (e) {
          // Ignore errors when closing
        }
      }
      // Wait for onclose event to nullify ws
      // this.ws will be set to null in onclose handler
    }

    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  _handleConnectionOpen() {
    clearTimeout(this.connectionTimeout);
    this.connected = true;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.lastPongTime = Date.now();

    // Start ping/pong for connection keepalive
    this.startPingPong();

    // Resubscribe to all channels
    this.resubscribeAll();

    // Dispatch connection event
    window.dispatchEvent(
      new CustomEvent(`websocket-connected-${this.exchange.toLowerCase()}`, {
        detail: { timestamp: Date.now() },
      }),
    );
  }

  _handleMessage(e) {
    // Update timestamps for connection health monitoring
    const now = Date.now();
    this.lastMessageTime = now;
    this.lastPongTime = now; // Update pong time on any message

    try {
      let data;

      // Fast path for string data (most common case)
      if (typeof e.data === "string") {
        // Try direct parsing first - most messages will be valid JSON
        try {
          data = JSON.parse(e.data);
        } catch (parseError) {
          // Only attempt recovery for specific error types
          if (parseError.message.includes("position")) {
            // Attempt to recover from position-based JSON errors
            const posMatch = parseError.message.match(/position (\d+)/i);
            if (posMatch && posMatch[1]) {
              const errorPos = parseInt(posMatch[1]);
              // Truncate the string slightly before the error position
              const safePos = Math.max(0, errorPos - 10);
              const truncated = e.data.substring(0, safePos);
              // Try to close the JSON properly
              const fixedJson = truncated + "]}"; // Simple fix attempt
              try {
                data = JSON.parse(fixedJson);
                // Don't log recovery to reduce console noise
              } catch (e) {
                // If recovery failed, throw a more specific error
                throw new Error(
                  `Failed to recover malformed JSON: ${parseError.message}`,
                );
              }
            } else {
              throw parseError;
            }
          } else {
            throw parseError;
          }
        }
      } else if (e.data instanceof ArrayBuffer) {
        // Handle binary data for Bybit - use cached decoder if available
        if (!this._textDecoder) {
          this._textDecoder = new TextDecoder();
        }
        const rawData = this._textDecoder.decode(e.data);
        try {
          data = JSON.parse(rawData);
        } catch (parseError) {
          throw new Error(`Binary data parse error: ${parseError.message}`);
        }
      } else {
        // Handle other data types (Blob, etc.) - should be rare
        throw new Error(`Unsupported message data type: ${typeof e.data}`);
      }

      // Fast return for pong messages
      if (data.op === "pong") return;

      // Handle whale/liquidation alert logic
      this._handleAlertLogic(data);

      // Process message with the callback if registered
      // this.messageCallback was removed as setMessageCallback is unused.

      // Process subscriptions - most important part
      this.processMessage(data);
    } catch (error) {
      // Only log errors at a throttled rate to prevent console spam
      this.throttledLog(
        "error",
        `Error processing ${this.exchange} message: ${error.message}`,
      );

      // Report to ErrorManager if available
      if (window.ErrorManager) {
        window.ErrorManager.reportError("websocket", error, {
          exchange: this.exchange,
          name: this.name,
        });
      }
    }
  }

  // Network status change handler
  _handleNetworkStatusChange(isOnline) {
    this.throttledLog(
      "network",
      `Network status changed: ${isOnline ? "online" : "offline"}`,
    );
    this.networkStatus = isOnline;

    if (isOnline) {
      // We're back online, check connection and reconnect if needed
      if (!this.isConnected()) {
        this.throttledLog(
          "network",
          `Network restored, reconnecting ${this.exchange} WebSocket`,
        );
        // Reset reconnect attempts to ensure quick reconnection
        this.reconnectAttempts = 0;
        this.reconnect(true);
      }
    } else {
      // We're offline, no need to keep trying to reconnect
      this.throttledLog(
        "network",
        `Network offline, pausing ${this.exchange} WebSocket reconnection`,
      );
      this._cleanupExistingConnection();
    }
  }

  // Visibility change handler
  _handleVisibilityChange() {
    const now = Date.now();

    if (document.visibilityState === "hidden") {
      this.lastHiddenTime = now;

      this.stopPingPong(); // Pause main ping timer

    } else if (document.visibilityState === "visible") {
      // Debounce reconnects on rapid visibility changes
      if (this._visibilityReconnectDebounce) {
        return;
      }
      this._visibilityReconnectDebounce = true;
      setTimeout(() => {
        this._visibilityReconnectDebounce = false;
      }, 2000);

      // lightCleanupTimeout related logic removed as performGlobalLightCleanup is no longer used.
      // if (this.lightCleanupTimeout) {
      //   clearTimeout(this.lightCleanupTimeout);
      //   this.lightCleanupTimeout = null;
      // }

      let hiddenDuration = 0;
      if (this.lastHiddenTime) {
        hiddenDuration = now - this.lastHiddenTime;
      }

      // Call global restoreUIStateFromCache
      // window.restoreUIStateFromCache(); // Removed call to stub

      // Robust connection check
      if (!this.isConnected()) {
        this.reconnect(true); // force reconnect, reset attempts
      } else if (hiddenDuration > 45000) {
        // 45 seconds
        this.reconnect(true);
      } else {
        this._sendPing(); // Verify connection

        const connectedAfterPing = this.isConnected();

        if (connectedAfterPing) {
          this.resubscribeAll();
        }
      }

      this.lastHiddenTime = null;

      // Restart main ping timer if connected
      if (this.isConnected()) {
        // Check isConnected before starting ping pong
        this.startPingPong();
      }
    }
  }

  // Send ping to check connection
  _sendPing() {
    if (!this.connected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      switch (this.exchange) {
        case "bybit":
          this.ws.send(JSON.stringify({ op: "ping" }));
          break;
        case "bitstamp":
          // Bitstamp does not support server-side ping/pong via WebSocket messages.
          // Its connection liveness is checked in startPingPong via lastMessageTimestamp
          // and by updating lastPongTime if the connection is merely OPEN.
          // So, _sendPing for Bitstamp is a no-op if connection is otherwise fine.
          break;
        default:
          // Fallback for other exchanges
          this.ws.send(JSON.stringify({ op: "ping" }));
      }
    } catch (error) {
      this.throttledLog(
        "ping",
        `Error sending ping to ${this.exchange}: ${error}`,
      );
      this.reconnect(true);
    }
  }

  //+------------------------------------------------------------------------+
  //| Payload Creation Helpers                                               |
  //+------------------------------------------------------------------------+

  _createBitstampSubscribeMessage(channel) {
    return {
      event: "bts:subscribe",
      data: { channel },
    };
  }

  _createBybitSubscribeMessage(channelsArray) {
    return {
      op: "subscribe",
      args: channelsArray,
    };
  }

  _createBitstampUnsubscribeMessage(channel) {
    return {
      event: "bts:unsubscribe",
      data: { channel },
    };
  }

  _createBybitUnsubscribeMessage(channelsArray) {
    return {
      op: "unsubscribe",
      args: channelsArray,
    };
  }

  //+------------------------------------------------------------------------+
  //| Alert Logic Helper                                                     |
  //+------------------------------------------------------------------------+

  _handleAlertLogic(data) {
    // --- DEDUPLICATION STATE ---
    if (!window._wsDedup) {
      window._wsDedup = {
        liquidations: new Set(),
        whales: new Set(),
        clearOld: function (set, ms = 5000) {
          const now = Date.now();
          const toDelete = [];
          for (const key of set) {
            const [ts] = key.split("|");
            if (now - Number(ts) > ms) toDelete.push(key);
          }
          // Batch delete operations for better performance
          toDelete.forEach((key) => set.delete(key));
        },
      };
    }

    // Whale/Liquidation alert logic (Bybit)
    if (
      this.exchange === "bybit" &&
      data.topic?.startsWith("liquidation.") &&
      data.data
    ) {
      const liquidation = Array.isArray(data.data) ? data.data[0] : data.data;
      const side = liquidation.side === "Buy" ? "LONG" : "SHORT";
      const price = parseFloat(liquidation.price);
      const size = parseFloat(liquidation.size || liquidation.qty);
      const value = price * size;

      // Always get the latest threshold from localStorage or window.consoleMessageThreshold
      let liqThreshold =
        (typeof window.consoleMessageThreshold !== "undefined" &&
          window.consoleMessageThreshold) ||
        (typeof localStorage !== "undefined" &&
          localStorage.getItem("liquidationThreshold") &&
          parseFloat(localStorage.getItem("liquidationThreshold"))) ||
        100000;

      if (value >= liqThreshold) {
        const ts = Date.now();
        const dedupKey = `${Math.round(ts / 2000) * 2000}|${price}|${size}|${side}`;
        window._wsDedup.clearOld(window._wsDedup.liquidations, 5000);

        if (!window._wsDedup.liquidations.has(dedupKey)) {
          window._wsDedup.liquidations.add(dedupKey);
          if (window.consoleCaptureAddMessage) {
            // Always pass numeric value as tradeSize for strict filtering
            const formattedValue =
              window.utils && window.utils.formatLargeNumber
                ? window.utils.formatLargeNumber(value)
                : value.toLocaleString(undefined, { maximumFractionDigits: 0 });
            window.consoleCaptureAddMessage(
              `L $${formattedValue}`,
              side === "LONG" ? "long" : "short",
              value,
            );
          }
        }
      }
    }

    // Whale alert logic (Bitstamp)
    if (
      this.exchange === "bitstamp" &&
      data.channel?.startsWith("live_trades_") &&
      data.event === "trade" &&
      data.data
    ) {
      const trade = data.data;
      const price = parseFloat(trade.price);
      const size = parseFloat(trade.amount || trade.size || trade.qty);

      // Use Bitstamp trade.type field for buy/sell detection
      let side = "";
      if ("type" in trade) {
        // According to Bitstamp docs: type: 0 = buy, 1 = sell
        side = trade.type === 0 ? "BUY" : trade.type === 1 ? "SELL" : "";
      }

      if (side === "BUY" || side === "SELL") {
        const value = price * size;

        // Always get the latest threshold from localStorage or window.consoleMessageThreshold
        let whaleThreshold =
          (typeof window.consoleMessageThreshold !== "undefined" &&
            window.consoleMessageThreshold) ||
          (typeof localStorage !== "undefined" &&
            localStorage.getItem("whaleAlertThreshold") &&
            parseFloat(localStorage.getItem("whaleAlertThreshold"))) ||
          100000;

        if (value >= whaleThreshold) {
          const ts = Date.now();
          const dedupKey = `${Math.round(ts / 2000) * 2000}|${price}|${size}|${side}`;
          window._wsDedup.clearOld(window._wsDedup.whales, 5000);

          if (!window._wsDedup.whales.has(dedupKey)) {
            window._wsDedup.whales.add(dedupKey);
            if (window.consoleCaptureAddMessage) {
              // Always pass numeric value as tradeSize for strict filtering
              const formattedValue =
                window.utils && window.utils.formatLargeNumber
                  ? window.utils.formatLargeNumber(value)
                  : value.toLocaleString(undefined, {
                      maximumFractionDigits: 0,
                    });
              const whaleType = side === "BUY" ? "whale-buy" : "whale-sell";
              window.consoleCaptureAddMessage(
                `T $${formattedValue}`,
                whaleType,
                value,
              );
            }
          }
        }
      }
    }
  }
}

// Create just one manager per exchange
window.bitstampWsManager = new WebSocketManager(
  "wss://ws.bitstamp.net",
  "bitstamp",
);
window.bybitWsManager = new WebSocketManager(
  "wss://stream.bybit.com/v5/public/linear",
  "bybit",
);

// Make the class available globally
window.WebSocketManager = WebSocketManager;

// export default WebSocketManager;
