(function () {
  // === Utility Functions ===
  function logError(message, error) {
    console.error(`${message}: ${error.message}`);
    // Optional: Add integration with an error reporting service like Sentry
  }

  // Use global utility for formatting large numbers
  const formatLargeNumber = window.commonUtils.formatLargeNumber;

  // Use centralized throttle function
  const throttle = window.commonUtils.throttle;

  // tryFetchWithProxies, fetchBitstampData, fetchBybitData, fetchMarketData are removed, will use window.dataFetcher

  // Register cleanup for any global references if set in this module
  if (window.CleanupManager && window.CleanupManager.registerCleanup) {
    window.CleanupManager.registerCleanup(() => {
      // Example: if you attach anything to window, clean it here
      // window.popupChartModule = null;
    });
  }

  // Drawing Primitives and MarkerManager were removed as they are not used by window.popupChart directly
  // and UI/drag logic is in popupChartUI.js

  // === Popup Chart Object ===
  (() => {
    // --- BitstampBTCWsManager (local to popup-chart.js) ---
    class BitstampBTCWsManager {
      constructor() {
        this.ws = null;
        this.connected = false;
        this.subscriptions = {};
        this.handlers = {};
        this.connecting = false;
        this.url = "wss://ws.bitstamp.net";
        this._reconnectTimeout = null;
      }
      isConnected() {
        return this.connected;
      }
      connect() {
        if (this.connected || this.connecting) return;
        this.connecting = true;
        this.ws = new WebSocket(this.url);
        this.ws.onopen = () => {
          console.log('[BitstampBTCWsManager] WebSocket connection opened.');
          this.connected = true;
          this.connecting = false;
          Object.keys(this.subscriptions).forEach((channel) => {
            this.ws.send(
              JSON.stringify({ event: "bts:subscribe", data: { channel } }),
            );
          });
        };
        this.ws.onclose = (event) => { // Added event parameter
          console.warn('[BitstampBTCWsManager] WebSocket connection closed.', { code: event.code, reason: event.reason, wasClean: event.wasClean });
          this.connected = false;
          this.connecting = false;
          this._scheduleReconnect();
        };
        this.ws.onerror = (event) => { // Added event parameter
          console.error('[BitstampBTCWsManager] WebSocket error:', event);
          this.connected = false;
          this.connecting = false;
          this._scheduleReconnect();
        };
        this.ws.onmessage = (msg) => {
          let data;
          try {
            data = JSON.parse(msg.data);
          } catch (error) { // Added error parameter
            console.error('[BitstampBTCWsManager] Error parsing WebSocket message:', error, 'Raw data:', msg.data);
            return; // Skip further processing of unparseable message
          }
          const channel = data.channel;
          if (channel && this.handlers[channel]) {
            this.handlers[channel].forEach((fn) => fn(data));
          }
        };
      }
      _scheduleReconnect() {
        if (this._reconnectTimeout) clearTimeout(this._reconnectTimeout);
        console.log('[BitstampBTCWsManager] Scheduling reconnection in 3 seconds...');
        this._reconnectTimeout = setTimeout(() => this.connect(), 3000);
      }
      subscribe(channel, handler) {
        if (!this.subscriptions[channel]) {
          this.subscriptions[channel] = true;
          this.handlers[channel] = [];
          if (this.connected && this.ws) {
            this.ws.send(
              JSON.stringify({ event: "bts:subscribe", data: { channel } }),
            );
          }
        }
        if (handler && typeof handler === "function") {
          this.handlers[channel].push(handler);
        }
      }
      unsubscribe(channel) {
        if (this.subscriptions[channel]) {
          delete this.subscriptions[channel];
          delete this.handlers[channel];
          if (this.connected && this.ws) {
            this.ws.send(
              JSON.stringify({ event: "bts:unsubscribe", data: { channel } }),
            );
          }
        }
      }
    }

    // Create a local manager for the popup/chart window
    window.orderBooksBitstampWsManager = new BitstampBTCWsManager();
    window.orderBooksBitstampWsManager.connect();

    window.popupChart = {
      // State variables
      chart: null,
      series: null,
      container: null,
      // chartData is now used ONLY for caching historical API data, not for bar storage
      chartData: {},
      barArray: [],
      isInitializing: false,
      currentSymbol: null,
      currentInterval: window.CONFIG?.popupChart?.defaultInterval || "60",
      currentBar: null,
      // _pendingBarUpdate: null, // Removed for throttling reversion
      // _updateTimeoutId: null,  // Removed for throttling reversion
      // THROTTLE_DELAY_MS: 200, // Removed for throttling reversion

      // Event handlers
      handleResize: null,
      wsHandler: null,

      // === Methods ===

      /**
       * Initializes the chart with the specified symbol and interval.
       * @param {string} symbol - The market symbol (e.g., 'BTCUSD').
       * @param {string} interval - The timeframe interval (e.g., '60' for 1 hour).
       */
      initialize: function (symbol, interval) {
        if (this.isInitializing) {
          // Popup chart initialization already in progress, skipping
          return;
        }
        // Initializing popup chart
        this.isInitializing = true;

        try {
          this.cleanup();
          this.container = document.getElementById("popup-chart-container");
          if (!this.container) {
            throw new Error("Popup chart container not found");
          }
          this.container.style.display = "block";

          // Set initial popup position if not set (for stable drag)
          const wrapper = document.getElementById("popup-chart-wrapper");
          if (
            wrapper &&
            (wrapper.style.left === "" || wrapper.style.top === "")
          ) {
            wrapper.style.left = "10px";
            wrapper.style.top = "10px";
          }

          const width =
            this.container.clientWidth ||
            window.CONFIG?.popupChart?.defaultWidth ||
            400;
          const height =
            this.container.clientHeight ||
            window.CONFIG?.popupChart?.defaultHeight ||
            300;

          if (typeof LightweightCharts === "undefined") {
            throw new Error("LightweightCharts library not loaded");
          }

          const chartColors = window.CONFIG?.chart?.defaultColors || {
            background: "#0f141a",
            text: "#D3D3D3",
            grid: "#2A2A2A",
          };

          this.chart = LightweightCharts.createChart(this.container, {
            width: width,
            height: height,
            layout: {
              background: { color: chartColors.background, type: "solid" },
              textColor: chartColors.text,
              fontSize: 12,
              attributionLogo: false,
            },
            grid: {
              vertLines: { visible: false }, // Could be configured: chartColors.grid
              horzLines: { visible: false }, // Could be configured: chartColors.grid
            },
            timeScale: {
              timeVisible: true,
              secondsVisible: false,
              borderColor: chartColors.grid,
            },
            rightPriceScale: {
              borderColor: chartColors.grid,
              scaleMargins: { top: 0.1, bottom: 0.1 },
            },
          });

          // Set up ResizeObserver for the container
          if (typeof ResizeObserver !== "undefined") {
            const resizeObserver = new ResizeObserver((entries) => {
              for (const entry of entries) {
                const { width, height } = entry.contentRect;
                if (width > 50 && height > 50) {
                  // Resizing chart
                  this.chart.resize(width, height);
                }
              }
            });
            resizeObserver.observe(this.container);
            this.resizeObserver = resizeObserver;
          }

          // Creating candlestick series
          const candleStickColors = window.CONFIG?.chart?.candlestick || {
            upColor: "#AAAAAA",
            downColor: "#AAAAAA",
            borderColor: "#AAAAAA",
            wickUpColor: "#AAAAAA",
            wickDownColor: "#AAAAAA",
          };
          this.series = this.chart.addSeries(
            LightweightCharts.CandlestickSeries,
            {
              ...candleStickColors,
              lastValueVisible: true,
              priceLineVisible: true,
              priceLineSource: LightweightCharts.PriceLineSource.LastBar,
              priceFormat: { type: "price", precision: 2, minMove: 0.01 },
            },
          );

          // Hide crosshair when mouse leaves chart area
          this.chart.subscribeCrosshairMove((param) => {
            if (!param.point) {
              // Hide crosshair: set mode to hidden
              this.chart.applyOptions({
                crosshair: { mode: LightweightCharts.CrosshairMode.Hidden },
              });
            } else {
              // Restore crosshair: set mode to normal
              this.chart.applyOptions({
                crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
              });
            }
          });

          this.currentSymbol = symbol;
          this.currentInterval = interval || "60";
          this.loadChartData(symbol, this.currentInterval);

          // Subscribe to websocket updates
          this.subscribeToWebSocket();

          window.addEventListener("resize", this.handleResize);
          if (typeof ResizeObserver !== "undefined") {
            const resizeObserver = new ResizeObserver(() =>
              this.handleResize(),
            );
            resizeObserver.observe(this.container);
            this.resizeObserver = resizeObserver;
          }
          // Popup chart initialized successfully
        } catch (error) {
          logError("Error initializing popup chart", error);
        } finally {
          this.isInitializing = false;
        }
      },

      /**
       * Handles chart resizing with throttling.
       */
      handleResize: throttle(function () {
        if (!this.chart || !this.container) return;
        try {
          const width = this.container.clientWidth || 400;
          const height = this.container.clientHeight || 300;
          if (width > 50 && height > 50) {
            // Resizing chart
            this.chart.resize(width, height);
          }
        } catch (error) {
          logError("Error resizing chart", error);
        }
      }, 100),

      /**
       * Updates the chart timeframe and reloads data.
       * @param {string} interval - The new interval to switch to.
       */
      updateTimeframe: function (interval) {
        if (!this.currentSymbol) {
          logError(
            "No current symbol found for popup chart",
            new Error("Missing symbol"),
          );
          return;
        }
        if (this.currentInterval === interval) {
          // Already using the same interval
          return;
        }

        // Updating popup chart timeframe

        // Unsubscribe from old interval's websocket
        this.unsubscribeFromWebSocket();

        this.currentInterval = interval;
        this.currentBar = null;
        this.currentBarVolume = 0; // Reset volume accumulator
        this.loadChartData(this.currentSymbol, interval);

        // Subscribe to new interval's websocket
        this.subscribeToWebSocket();
      },

      /**
       * Cleans up the chart and removes event listeners.
       */
      cleanup: function () {
        // Cleaning up popup chart
        try {
          // Unsubscribe from websocket updates
          this.unsubscribeFromWebSocket();

          window.removeEventListener("resize", this.handleResize);
          if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
          }

          // Clear chart data and state
          this.barArray = [];
          this.currentBar = null;     // Explicitly nullify
          this.currentBarVolume = 0;  // Explicitly reset
          this.wsHandler = null;      // Already there, good
          this.chartData = {};      // Clear internal cache

          // Clear throttled update state (Removed for throttling reversion)
          // if (this._updateTimeoutId) {
          //   clearTimeout(this._updateTimeoutId);
          //   this._updateTimeoutId = null;
          // }
          // this._pendingBarUpdate = null;

          // Properly clean up series
          if (this.series) {
            // No need to call series.setData([]) if chart is being removed
            this.series = null;
          }

          if (this.chart) {
            try {
              this.chart.remove();
            } catch (e) {
              logError("Error removing chart", e);
            }
            this.chart = null;
          }

          // Clear container
          if (this.container) {
            this.container.innerHTML = "";
            this.container.style.display = "none";
          }
          this.container = null;

          // Cleanup completed successfully
        } catch (error) {
          logError("Error during cleanup", error);
        }
      },

      /**
       * Loads and displays chart data for the specified symbol and interval.
       * Uses cached data if available, otherwise fetches from APIs with fallback.
       * @param {string} symbol - The market symbol (e.g., 'BTCUSD').
       * @param {string} interval - The timeframe interval (e.g., '60' for 1 hour).
       * @param {boolean} [isLineSeries=false] - Whether to display as a line series instead of candlesticks.
       */
      loadChartData: function (symbol, interval, isLineSeries = false) {
        if (!this.series || !this.chart) {
          logError(
            "Cannot load data - chart or series not initialized",
            new Error("Missing chart or series"),
          );
          return;
        }
        // Loading chart data

        const existingIndicator = document.getElementById(
          "chart-loading-indicator",
        );
        if (existingIndicator) existingIndicator.remove();
        this.showLoadingIndicator(this.container);

        const formattedSymbol = symbol.replace("USD", "").toLowerCase() + "usd";
        const apiInterval = this.mapIntervalToApi(interval);
        const cacheKey = `${formattedSymbol}_${apiInterval}`;

        // Determine exchange and fetch parameters early so they can be used in cached data section
        const exchange = formattedSymbol.includes("usd") ? "bitstamp" : "bybit";
        const fetchSymbol =
          exchange === "bitstamp"
            ? formattedSymbol
            : symbol.replace("USD", "").toUpperCase();

        // Ensure apiInterval is in the correct format for the chosen exchange/dataFetcher
        let fetchInterval = apiInterval;
        if (exchange === "bybit") {
          // Convert seconds to Bybit interval string if apiInterval is numeric
          if (typeof apiInterval === "number") {
            fetchInterval = this.mapApiIntervalToString(apiInterval);
          }
        }

        const cachedData = this.getCachedData(cacheKey);

        if (cachedData && cachedData.length > 0) {
          // Using cached data
          document.getElementById("chart-loading-indicator")?.remove();
          this.processChartData(cachedData, isLineSeries);

          const controller = new AbortController();
          const signal = controller.signal;

          setTimeout(
            () =>
              window.dataFetcher.getHistoricalData(
                exchange,
                fetchSymbol,
                fetchInterval,
                signal,
              )
                .then((freshData) => {
                  if (
                    freshData &&
                    freshData.length > 0 &&
                    this.currentSymbol === symbol &&
                    this.currentInterval === interval
                  ) {
                    this.cacheData(cacheKey, freshData);
                    this.processChartData(freshData, isLineSeries);
                  }
                })
                .catch((error) =>
                  logError("Background data fetch error", error),
                ),
            100,
          );
          return;
        }

        const controller = new AbortController();
        const signal = controller.signal;
        const timeoutMs = window.CONFIG?.ui?.loadingTimeout || 7000;
        const raceTimeoutMs = timeoutMs + 1000; // Give a little extra for the race
        const abortTimeout = setTimeout(() => controller.abort(), timeoutMs);

        Promise.race([
          window.dataFetcher.getHistoricalData(
            exchange,
            fetchSymbol,
            fetchInterval,
            signal,
          ),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Data fetch timeout")),
              raceTimeoutMs,
            ),
          ),
        ])
          .then((data) => {
            clearTimeout(abortTimeout);
            document.getElementById("chart-loading-indicator")?.remove();
            if (window.performanceOptimizer && typeof window.performanceOptimizer.scheduleWork === 'function') {
                window.performanceOptimizer.scheduleWork(() => {
                    this.processFetchedData(data, cacheKey, isLineSeries);
                }, 'idle');
            } else {
                // Fallback if performanceOptimizer is not available
                this.processFetchedData(data, cacheKey, isLineSeries);
            }
            // setTimeout(() => this.fetchAdditionalData(formattedSymbol, apiInterval, data), 200); // fetchAdditionalData might need similar refactor
          })
          .catch((error) => {
            clearTimeout(abortTimeout);
            logError("Error fetching market data via dataFetcher", error);
            document.getElementById("chart-loading-indicator")?.remove();
            if (this.container) {
              const errorMessage = document.createElement("div");
              errorMessage.id = "chart-error-message";
              errorMessage.style.cssText =
                "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
              errorMessage.textContent =
                "Failed to load chart data. Check console.";
              this.container.appendChild(errorMessage);
              setTimeout(() => errorMessage.remove(), 3000);
            }
          });
      },

      /**
       * Shows a loading indicator in the chart container.
       * @param {HTMLElement} container - The chart container element.
       */
      showLoadingIndicator: function (container) {
        if (!container) return;
        // Check for existing loading indicators in one query
        const existingLoading = document.querySelector(
          "#popup-chart-loading, #popup-chart-priority-loading, #chart-loading-indicator",
        );
        if (existingLoading) return;

        const loadingIndicator = document.createElement("div");
        loadingIndicator.id = "chart-loading-indicator";
        loadingIndicator.style.cssText =
          "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
        loadingIndicator.textContent = "Loading chart data...";
        container.appendChild(loadingIndicator);
      },

      /**
       * Processes and displays the fetched chart data.
       * @param {Array} data - The chart data to display.
       * @param {string} cacheKey - The key for caching the data.
       * @param {boolean} isLineSeries - Whether to display as a line series.
       */
      processFetchedData: function (data, cacheKey, isLineSeries) {
        if (!data || data.length === 0) {
          throw new Error("No data received from API");
        }
        // Ensure all bars have valid volume
        const safeData = data.map((b) => ({
          ...b,
          volume: Number.isFinite(b.volume) ? b.volume : 0,
        }));
        // Received data from API
        this.cacheData(cacheKey, safeData);
        this.processChartData(safeData, isLineSeries);
      },

      mapApiIntervalToString: function (intervalSeconds) {
        // Helper to convert seconds to Bybit's string interval if needed by dataFetcher
        // This complements mapIntervalToApi which returns seconds.
        const reverseIntervalMap = {
          60: "1",
          300: "5",
          900: "15",
          1800: "30",
          3600: "60",
          14400: "240",
          86400: "D",
          604800: "W",
          2592000: "M",
        };
        return (
          reverseIntervalMap[intervalSeconds] || intervalSeconds.toString()
        ); // Fallback or pass as is
      },

      /**
       * Maps chart interval to API-compatible interval.
       * @param {string} interval - The chart interval (e.g., '60').
       * @returns {number} The API interval in seconds.
       */
      mapIntervalToApi: function (interval) {
        const intervals = {
          1: 60, // 1 minute
          5: 300, // 5 minutes
          15: 900, // 15 minutes
          30: 1800, // 30 minutes
          60: 3600, // 1 hour
          240: 14400, // 4 hours
          D: 86400, // 1 day
          "1D": 86400, // 1 day (alternate)
          "1W": 604800, // 1 week
          "1M": 2592000, // 1 month (30 days)
        };
        return intervals[interval] || 3600; // Default to 1 hour in seconds
      },

      /**
       * Caches chart data for future use.
       * @param {string} key - The cache key.
       * @param {Array} data - The data to cache.
       */
      cacheData: function (key, data) {
        this.chartData[key] = data;
      },

      /**
       * Retrieves cached chart data.
       * @param {string} key - The cache key.
       * @returns {Array|null} The cached data or null if not found.
       */
      getCachedData: function (key) {
        return this.chartData[key] || null;
      },

      /**
       * Processes chart data and updates the series.
       * @param {Array} data - The data to process.
       * @param {boolean} isLineSeries - Whether to display as a line series.
       */
      processChartData: function (data, isLineSeries) {
        if (!this.series) {
          return;
        }
        if (isLineSeries) {
          const lineData = data.map((d) => ({ time: d.time, value: d.close }));
          try {
            this.series.setData(lineData);
          } catch (e) {
            logError('Error setting line data in processChartData', e);
          }
          this.barArray = [];
        } else {
          // Defensive: log and check data for 1D and 1W intervals
          if (this.currentInterval === "D" || this.currentInterval === "1D" || this.currentInterval === "1W") {
            if (!Array.isArray(data) || data.length === 0) {
              console.error(`[popupChart] No data provided to setData for interval ${this.currentInterval}.`);
            } else if (
              !("time" in data[0] && "open" in data[0] && "high" in data[0] && "low" in data[0] && "close" in data[0])
            ) {
              console.error(`[popupChart] Data format invalid for interval ${this.currentInterval}`);
            }
          }
          try {
            const filtered = window.utils.filterValidBars(data);
            if (filtered.length !== data.length) {
              console.error(
                `[popupChart] Invalid bars filtered out in processChartData for interval ${this.currentInterval}`,
                { original: data, filtered },
              );
            }
            if (filtered.length === 0) {
              // All bars were invalid, likely due to aggregation or data fetch issue
              logError(`[popupChart] All bars filtered out for interval ${this.currentInterval}. Possible data/aggregation issue.`, new Error("No valid bars for chart"));
              // Optionally, show a user-friendly message
              if (this.container) {
                const errorMessage = document.createElement("div");
                errorMessage.id = "chart-error-message";
                errorMessage.style.cssText =
                  "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
                errorMessage.textContent = `No valid data for this timeframe.`;
                this.container.appendChild(errorMessage);
                setTimeout(() => errorMessage.remove(), 3000);
              }
              return;
            }
            // Check for any null/undefined in filtered
            if (filtered.some((b) => !b || typeof b !== "object")) {
              console.error(
                "[popupChart] Null/undefined bar detected after filtering",
                filtered,
              );
            }

            // Iterate through filtered bars and log errors for invalid ones
            filtered.forEach((bar, index) => {
              if (
                !bar || // Check if bar itself is null or undefined
                !Number.isFinite(bar.time) ||
                !Number.isFinite(bar.open) ||
                !Number.isFinite(bar.high) ||
                !Number.isFinite(bar.low) ||
                !Number.isFinite(bar.close) ||
                !Number.isFinite(bar.volume)
              ) {
                logError(
                  `[popupChart] Invalid bar found in 'filtered' data at index ${index} before setData`,
                  new Error(
                    `Invalid bar: ${JSON.stringify(bar)}`,
                  ),
                );
                // Depending on requirements, we might choose to filter out this specific bar again
                // or prevent setData altogether. For now, logging is the primary goal.
              }
            });

            try {
              this.series.setData(filtered);
            } catch (e) {
              logError('Error setting candlestick data in processChartData', e);
              // console.error previously existed, logError is now preferred.
              // Keep original console.error for now if specific debugging context was intended.
              console.error(
                "[popupChart] Error calling setData on series (candlestick)",
                e,
                data,
              );
            }
          } catch (e) {
            // This catch is for window.utils.filterValidBars or other logic inside the outer try
            logError('[popupChart] Error processing or filtering chart data', e);
            console.error( // Keep original console.error for now
              "[popupChart] Error in processChartData (outer try)",
              e,
              data,
            );
          }

          // Store historical bars
          this.barArray = data.slice(); // `data` is historical, `filtered` was used for the setData above.
                                      // Using `data` directly here assumes `filterValidBars` was for display mostly
                                      // and we want to keep original data for barArray if possible.
                                      // Or, use `filtered_historical_data` if that was stored.
                                      // For now, let's assume `data` is the source of truth for historical bars.

          // Initialize currentBar based on the last historical bar or create a new one.
          const now = Math.floor(Date.now() / 1000);
          const apiInterval = this.mapIntervalToApi(this.currentInterval);
          const currentCalculatedBarStartTime = Math.floor(now / apiInterval) * apiInterval;

          let lastHistoricalBar = this.barArray.length > 0 ? this.barArray[this.barArray.length - 1] : null;

          const localSafeNum = (val, fallback = 0) => (typeof val === 'number' && isFinite(val) ? val : fallback);

          if (lastHistoricalBar && lastHistoricalBar.time === currentCalculatedBarStartTime) {
            this.currentBar = { ...lastHistoricalBar };
            this.currentBarVolume = localSafeNum(lastHistoricalBar.volume);
          } else if (!lastHistoricalBar || currentCalculatedBarStartTime > lastHistoricalBar.time) {
            // Only create a new current bar if it's ahead of the last historical bar
            let openingPrice = lastHistoricalBar ? localSafeNum(lastHistoricalBar.close) : 0;
            this.currentBar = {
                time: currentCalculatedBarStartTime,
                open: openingPrice,
                high: openingPrice,
                low: openingPrice,
                close: openingPrice,
                volume: 0,
            };
            this.currentBarVolume = 0;
            this.barArray.push({ ...this.currentBar });
          } else {
            // If the last historical bar is newer than the calculated current bar, do not create a new bar
            this.currentBar = null;
            this.currentBarVolume = 0;
          }

          // Ensure barArray is not too long.
          if (this.barArray.length > 500) {
             this.barArray.shift();
          }

          // Update the series with the barArray (historical + current developing bar).
          const finalFilteredBars = window.utils.filterValidBars(this.barArray);
          try {
            this.series.setData(finalFilteredBars);
          } catch (e) {
            logError('Error setting data in processChartData (final with currentBar)', e);
          }
        }
        try {
          this.chart.timeScale().fitContent();
        } catch (e) {
          // Error fitting chart content
        }
      },

      /**
       * Fetches additional data for pagination or updates (stub method).
       * @param {string} symbol - The formatted symbol.
       * @param {number} interval - The API interval.
       * @param {Array} existingData - The current data.
       */
      fetchAdditionalData: function (symbol, interval, existingData) {
        // Stub for future pagination or real-time updates
        // Fetching additional data
      },

      /**
       * Subscribes to websocket updates for the current symbol.
       */
      subscribeToWebSocket: function () {
        if (!this.currentSymbol) return;
        if (!window.orderBooksBitstampWsManager) {
          // Retry after a short delay if the manager isn't ready yet
          setTimeout(() => this.subscribeToWebSocket(), 250);
          return;
        }
        const symbol = this.currentSymbol.replace("USD", "").toLowerCase();
        const channel = `live_trades_${symbol}usd`;
        if (!this.wsHandler) {
          this.wsHandler = (message) => {
            if (message.event === "trade" && message.data) {
              const price = parseFloat(message.data.price);
              const volume = parseFloat(message.data.amount);
              const type = message.data.type;

              if (Number.isFinite(price) && Number.isFinite(volume)) {
                this.updateRealTimeBar(price, volume, type === 0);
              }
            }
          };
        }

        window.orderBooksBitstampWsManager.subscribe(channel, this.wsHandler);
      },

      /**
       * Unsubscribes from websocket updates.
       */
      unsubscribeFromWebSocket: function () {
        if (
          !this.currentSymbol ||
          !window.orderBooksBitstampWsManager ||
          !this.wsHandler
        )
          return;
        const symbol = this.currentSymbol.replace("USD", "").toLowerCase();
        const channel = `live_trades_${symbol}usd`;
        window.orderBooksBitstampWsManager.unsubscribe(channel);
      },

      /**
       * Updates the current bar with real-time price data.
       */
      updateRealTimeBar: function (price, volume, isBuy) {
        if (!Number.isFinite(price) || !Number.isFinite(volume)) {
          logError('Invalid price or volume in updateRealTimeBar', new Error(`Price: ${price}, Volume: ${volume}`));
          return;
        }
        if (!this.series || !price) {
          return;
        }
        const now = Math.floor(Date.now() / 1000);
        const isWeekly = this.currentInterval === "1W" || this.currentInterval === 604800;
        const isMonthly = this.currentInterval === "1M" || this.currentInterval === 2592000;
        // --- 1W/1M logic only ---
        if (isWeekly || isMonthly) {
          if (!this.barArray.length) return;
          const lastBar = this.barArray[this.barArray.length - 1];
          const getStartOfWeek = (timestampSeconds) => {
            const date = new Date(timestampSeconds * 1000);
            const day = date.getUTCDay();
            const diff = (day + 6) % 7;
            date.setUTCDate(date.getUTCDate() - diff);
            date.setUTCHours(0, 0, 0, 0);
            return Math.floor(date.getTime() / 1000);
          };
          const getStartOfMonth = (timestampSeconds) => {
            const date = new Date(timestampSeconds * 1000);
            date.setUTCDate(1);
            date.setUTCHours(0, 0, 0, 0);
            return Math.floor(date.getTime() / 1000);
          };
          const samePeriod = isWeekly
            ? getStartOfWeek(now) === getStartOfWeek(lastBar.time)
            : getStartOfMonth(now) === getStartOfMonth(lastBar.time);
          if (samePeriod) {
            lastBar.high = Math.max(lastBar.high, price);
            lastBar.low = Math.min(lastBar.low, price);
            lastBar.close = price;
            lastBar.volume += volume;
            this.currentBar = { ...lastBar };
            this.currentBarVolume = lastBar.volume;
            this.barArray[this.barArray.length - 1] = { ...lastBar };
            if (typeof lastBar.time === 'number') {
              try {
                this.series.update({ ...lastBar });
              } catch (e) {
                logError('[popupChart] Error in series.update (real-time, 1W/1M)', e);
              }
            } else {
              logError('[popupChart] Skipped update: lastBar.time is not a number', new Error(JSON.stringify(lastBar)));
            }
          }
          return;
        }
        // --- default logic for 1D and intraday ---
        const interval = this.mapIntervalToApi(this.currentInterval);
        const barTime = Math.floor(now / interval) * interval;
        function safeNum(val, fallback = 0) {
          return typeof val === "number" && isFinite(val) ? val : fallback;
        }
        if (typeof this.currentBarVolume !== "number")
          this.currentBarVolume = 0;
        if (!this.currentBar || this.currentBar.time < barTime) {
          if (this.currentBar) {
            const prevBarIndex = this.barArray.findIndex(b => b.time === this.currentBar.time);
            if (prevBarIndex !== -1) {
              this.barArray[prevBarIndex].volume = safeNum(this.currentBarVolume);
            } else {
              logError('[popupChart] Previous currentBar not found in barArray for volume finalization', new Error(`Bar time: ${this.currentBar.time}`));
            }
          }
          this.currentBarVolume = safeNum(volume);
          this.currentBar = {
            time: safeNum(barTime),
            open: safeNum(price),
            high: safeNum(price),
            low: safeNum(price),
            close: safeNum(price),
            volume: safeNum(volume),
          };
          const existingBarIndex = this.barArray.findIndex(b => b.time === this.currentBar.time);
          if (existingBarIndex !== -1) {
            this.barArray.splice(existingBarIndex, 1);
          }
          this.barArray.push({ ...this.currentBar });
          if (this.barArray.length > 500) {
            this.barArray.shift();
          }
          try {
            if (
              !Number.isFinite(this.currentBar.time) ||
              !Number.isFinite(this.currentBar.open) ||
              !Number.isFinite(this.currentBar.high) ||
              !Number.isFinite(this.currentBar.low) ||
              !Number.isFinite(this.currentBar.close) ||
              !Number.isFinite(this.currentBar.volume)
            ) {
              logError("[popupChart] Attempted to update series with invalid new bar properties", new Error(`Invalid new bar: ${JSON.stringify(this.currentBar)}`));
            } else {
              this.series.update({ ...this.currentBar });
            }
          } catch (e) {
            logError('[popupChart] Error in series.update (new bar)', e);
          }
        } else {
          this.currentBar.high = Math.max(safeNum(this.currentBar.high), safeNum(price));
          this.currentBar.low = Math.min(safeNum(this.currentBar.low), safeNum(price));
          this.currentBar.close = safeNum(price);
          this.currentBarVolume += safeNum(volume);
          this.currentBar.volume = safeNum(this.currentBarVolume);
          const barIndex = this.barArray.findIndex(b => b.time === this.currentBar.time);
          if (barIndex !== -1) {
            this.barArray[barIndex] = { ...this.currentBar };
          } else {
            logError('[popupChart] currentBar not found in barArray for update', new Error(`Bar time: ${this.currentBar.time}. Pushing as new.`));
            this.barArray.push({ ...this.currentBar });
            if (this.barArray.length > 500) {
              this.barArray.shift();
            }
          }
          const barToUpdate = { ...this.currentBar };
          if (typeof barToUpdate.time === 'object' && barToUpdate.time !== null && 'time' in barToUpdate.time) {
            barToUpdate.time = barToUpdate.time.time;
          }
          if (
            !Number.isFinite(barToUpdate.time) ||
            !Number.isFinite(barToUpdate.open) ||
            !Number.isFinite(barToUpdate.high) ||
            !Number.isFinite(barToUpdate.low) ||
            !Number.isFinite(barToUpdate.close) ||
            !Number.isFinite(barToUpdate.volume)
          ) {
            logError("[popupChart] Attempted to update series with invalid bar properties", new Error(`Invalid bar: time=${barToUpdate.time}, open=${barToUpdate.open}, high=${barToUpdate.high}, low=${barToUpdate.low}, close=${barToUpdate.close}, volume=${barToUpdate.volume}`));
          } else {
            try {
              this.series.update(barToUpdate);
            } catch (e) {
              logError('[popupChart] Error in series.update (existing bar)', e);
            }
          }
        }

        // Monthly interval logic: update only if in the same month
        if (isMonthly) {
          if (!this.barArray.length) return;
          const lastBar = this.barArray[this.barArray.length - 1];
          // Use the same getStartOfMonth as in dataFetcher
          const getStartOfMonth = (timestampSeconds) => {
            const date = new Date(timestampSeconds * 1000);
            date.setUTCDate(1);
            date.setUTCHours(0, 0, 0, 0);
            return Math.floor(date.getTime() / 1000);
          };
          const now = Math.floor(Date.now() / 1000);
          const sameMonth = getStartOfMonth(now) === getStartOfMonth(lastBar.time);
          if (sameMonth) {
            // Update the last bar
            lastBar.high = Math.max(lastBar.high, price);
            lastBar.low = Math.min(lastBar.low, price);
            lastBar.close = price;
            lastBar.volume += volume;
            this.currentBar = { ...lastBar };
            this.currentBarVolume = lastBar.volume;
            this.barArray[this.barArray.length - 1] = { ...lastBar };
            try {
              this.series.update({ ...lastBar });
            } catch (e) {
              logError('[popupChart] Error in series.update (real-time, 1W/1M)', e);
            }
          }
          return;
        }

        // Weekly interval logic: update only if in the same week
        if (isWeekly) {
          if (!this.barArray.length) return;
          const lastBar = this.barArray[this.barArray.length - 1];
          const getStartOfWeek = (timestampSeconds) => {
            const date = new Date(timestampSeconds * 1000);
            const day = date.getUTCDay();
            const diff = (day + 6) % 7;
            date.setUTCDate(date.getUTCDate() - diff);
            date.setUTCHours(0, 0, 0, 0);
            return Math.floor(date.getTime() / 1000);
          };
          const now = Math.floor(Date.now() / 1000);
          const sameWeek = getStartOfWeek(now) === getStartOfWeek(lastBar.time);
          if (sameWeek) {
            // Update the last bar
            lastBar.high = Math.max(lastBar.high, price);
            lastBar.low = Math.min(lastBar.low, price);
            lastBar.close = price;
            lastBar.volume += volume;
            this.currentBar = { ...lastBar };
            this.currentBarVolume = lastBar.volume;
            this.barArray[this.barArray.length - 1] = { ...lastBar };
            // Defensive: only update if time is a number
            if (typeof lastBar.time === 'number') {
              try {
                this.series.update({ ...lastBar });
              } catch (e) {
                logError('[popupChart] Error in series.update (real-time, 1W/1M)', e);
              }
            } else {
              logError('[popupChart] Skipped update: lastBar.time is not a number', new Error(JSON.stringify(lastBar)));
            }
          }
          // For 1W/1M, do not create a new bar in real time. Exit here.
          return;
        }
      },
    };

    // Listen for main chart pair switches and refresh popup chart if open
    document.addEventListener("chartSwitched", (e) => {
      const newPair = e.detail?.newPair;
      if (!newPair) return;
      // Only refresh if popup is visible/open
      const popupContainer = document.getElementById("popup-chart-container");
      if (popupContainer && popupContainer.style.display !== "none") {
        // Use the current popup interval or default to '60'
        const interval = window.popupChart.currentInterval || "60";
        window.popupChart.initialize(newPair, interval);
      }
    });

    // Draggable Popup Chart IIFE was removed as its logic is now in components/popupChartUI.js
  })();
})();
