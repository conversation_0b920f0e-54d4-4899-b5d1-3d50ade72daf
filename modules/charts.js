(() => {
  /**
   * Configuration object for chart settings with type-safe defaults.
   */
  const CONFIG = window.CONFIG?.indicators
    ? {
        barInterval: Number(window.CONFIG.indicators.barInterval) || 300,
        maxBars: Number(window.CONFIG.indicators.maxBars) || 6000,
        futureBars: Number(window.CONFIG.indicators.futureBars) || 20,
        emaPeriod: Number(window.CONFIG.indicators.emaPeriod) || 180,
        sdPeriod: Number(window.CONFIG.indicators.sdPeriod) || 1440,
        cacheTTL: Number(window.CONFIG.indicators.cacheTTL) || 300000,
      }
    : {
        barInterval: 300,
        maxBars: 6000,
        futureBars: 20,
        emaPeriod: 180,
        sdPeriod: 1440,
        cacheTTL: 300000,
      };

  const PAIRS = [
    "ADA",
    "AAVE",
    "AVAX",
    "DOGE",
    "DOT",
    "FIL",
    "LINK",
    "MATIC",
    "UNI",
    "XRP",
    "XLM",
    "MKR",
    "<PERSON>USH<PERSON>",
    "COMP",
    "CRV",
    "1INCH",
    "LRC",
    "FET",
    "DYDX",
    "INJ",
    "AXS",
    "GRT",
    "SNX",
    "YFI",
    "BAND",
    "KNC",
    "ENS",
    "CVX",
    "RNDR",
    "AUDIO",
    "NEXO",
    "PEPE",
    "PERP",
    "PYTH",
    "RAD",
    "GODS",
    "CTSI",
    "SKL",
    "FLR",
  ];
  const domCache = new Map();
  const chartStates = new Map();
  window.chartStates = chartStates;
  let currentPair = "BTC";
  window.currentPair = currentPair;

  window.CleanupManager &&
    window.CleanupManager.registerCleanup &&
    window.CleanupManager.registerCleanup(() => {
      window.chartStates = null;
      window.currentPair = null;
    });

  /**
   * Utility functions for mathematical and common operations.
   */
  const utils = {
    throttle: window.commonUtils.throttle,
    debounce: window.commonUtils.debounce,
    filterValidBars: (bars) => {
      if (!Array.isArray(bars)) {
        console.warn("Invalid bars array for filterValidBars:", bars);
        return [];
      }
      return bars.filter(
        (bar) =>
          bar &&
          Number.isFinite(bar.time) &&
          Number.isFinite(bar.open) &&
          Number.isFinite(bar.high) &&
          Number.isFinite(bar.low) &&
          Number.isFinite(bar.close) &&
          Number.isFinite(bar.volume),
      );
    },
  };

  /**
   * Handles errors with consistent logging and UI feedback.
   * @param {string} msg - Error message.
   * @param {Error} err - Error object.
   * @param {HTMLElement} overlay - Overlay element for UI feedback.
   */
  const handleError = (msg, err, overlay) => {
    console.error(`${msg}: ${err.message}`);
    if (overlay) overlay.textContent = `${msg}: ${err.message}`;
  };

  const container = document.querySelector(".chart-container");
  if (container) {
    domCache.set("container", container);
    domCache.set("overlay", container.querySelector(".loading-overlay"));
    domCache.set(
      "priceChartContainer",
      container.querySelector(".price-chart-container"),
    );
    domCache.set(
      "buttons",
      Array.from(container.querySelectorAll(".pair-button")),
    );
    domCache.set("pairSelector", container.querySelector(".pair-selector"));
    domCache.set("liqControls", container.querySelector(".liq-controls"));
    domCache.set(
      "liquidationConsole",
      document.getElementById("liquidation-console"),
    );
  }

  if (!window.eventBus) {
    window.eventBus = {
      events: {},
      subscribe: (e, cb) => {
        window.eventBus.events[e] = window.eventBus.events[e] || [];
        window.eventBus.events[e].push(cb);
        return () =>
          (window.eventBus.events[e] = window.eventBus.events[e].filter(
            (c) => c !== cb,
          ));
      },
      publish: (e, d) =>
        window.eventBus.events[e]?.forEach((cb) => {
          try {
            cb(d);
          } catch (err) {
            console.error(`Error in ${e}:`, err);
          }
        }),
    };
    PAIRS.forEach((p) => {
      const liqHandler = (e) =>
        e.detail && window.eventBus.publish(`liquidation-${p}`, e.detail);
      const whaleHandler = (e) =>
        e.detail && window.eventBus.publish(`whale-alert-${p}`, e.detail);
      window.addEventListener(`liquidation-${p}`, liqHandler);
      window.addEventListener(`whale-alert-${p}`, whaleHandler);
      window.CleanupManager &&
        window.CleanupManager.registerCleanup &&
        window.CleanupManager.registerCleanup(() => {
          window.removeEventListener(`liquidation-${p}`, liqHandler);
          window.removeEventListener(`whale-alert-${p}`, whaleHandler);
        });
    });
  }

  /**
   * Waits for LightweightCharts library to load with a fallback.
   * @returns {Promise<void>}
   */
  const waitForLightweightCharts = () =>
    new Promise((resolve, reject) => {
      let attempts = 0;
      let timeoutId = null;
      const check = () => {
        if (window.LightweightCharts) {
          setTimeout(resolve, 50);
        } else if (attempts++ < 50) {
          timeoutId = setTimeout(check, 100);
        } else {
          console.error(
            "LightweightCharts failed to load, attempting fallback...",
          );
          const script = document.createElement("script");
          script.src =
            "https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js";
          script.onload = () => resolve();
          script.onerror = () =>
            reject(new Error("All attempts to load LightweightCharts failed"));
          document.head.appendChild(script);
        }
      };
      check();
      window.CleanupManager &&
        window.CleanupManager.registerCleanup &&
        window.CleanupManager.registerCleanup(() => {
          if (timeoutId) clearTimeout(timeoutId);
        });
    });

  const HIST_DB_NAME = "crypto-dashboard-hist";
  const HIST_DB_VERSION = 1;
  const HIST_STORE = "bars";
  let histDb = null;

  /**
   * Initializes and returns the IndexedDB instance.
   * @returns {Promise<Object>} The IndexedDB wrapper instance.
   */
  async function getHistDb() {
    if (histDb) return histDb;
    if (window.IndexedDbWrapper) {
      histDb = new window.IndexedDbWrapper(HIST_DB_NAME, HIST_DB_VERSION, {
        [HIST_STORE]: { keyPath: "key" },
      });
      await histDb.ready;
      return histDb;
    }
    throw new Error("IndexedDbWrapper not loaded");
  }

  /**
   * Fetches historical data from Bitstamp with caching.
   * @param {string} pair - Trading pair.
   * @param {number} interval - Time interval in seconds.
   * @param {number} limit - Maximum number of bars.
   * @param {AbortSignal} abortSignal - Signal to abort fetch.
   * @returns {Promise<Array>} Array of OHLCV bars.
   */
  async function fetchBitstampHistoricalData(
    pair,
    interval,
    limit = 6000,
    abortSignal,
  ) {
    const cacheKey = `${pair}_historical_${interval}_${limit}_${Math.floor(Date.now() / CONFIG.cacheTTL)}`;
    let db;
    try {
      db = await getHistDb();
      const cached = await db.get(HIST_STORE, cacheKey);
      if (cached && cached.data.length >= limit * 0.9) {
        return cached.data.map((b) => ({
          time: b.t,
          open: b.o,
          high: b.h,
          low: b.l,
          close: b.c,
          volume: b.v,
        }));
      }
    } catch (e) {
      console.warn("Cache access failed, proceeding without cache:", e);
    }
    const maxApiLimit = 1000;
    let allBars = [],
      timeMap = new Map();
    const fetchBars = async (url) => {
      if (abortSignal?.aborted) throw new Error("Fetch aborted");
      const res = await fetch(url, { signal: abortSignal });
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      const data = await res.json();
      if (!data?.data?.ohlc) throw new Error("Invalid Bitstamp data");
      data.data.ohlc.forEach((b) => {
        const t = parseInt(b.timestamp, 10);
        if (!timeMap.has(t)) {
          timeMap.set(t, true);
          allBars.push({
            time: t,
            open: parseFloat(b.open),
            high: parseFloat(b.high),
            low: parseFloat(b.low),
            close: parseFloat(b.close),
            volume: parseFloat(b.volume),
          });
        }
      });
    };
    await fetchBars(
      `https://www.bitstamp.net/api/v2/ohlc/${pair}/?step=${interval}&limit=${maxApiLimit}`,
    );
    allBars.sort((a, b) => a.time - b.time);
    // Fetch additional historical data if needed
    for (let i = 0; i < 3 && allBars.length > 0 && allBars.length < limit; i++) {
      allBars.sort((a, b) => a.time - b.time);
      const earliest = allBars[0].time;
      const promises = [1, maxApiLimit * interval].map(offset =>
        fetchBars(`https://www.bitstamp.net/api/v2/ohlc/${pair}/?step=${interval}&limit=${maxApiLimit}&end=${earliest - offset}`)
          .then(() => allBars.length).catch(() => 0)
      );
      const results = await Promise.all(promises);
      if (results.every(r => r === 0)) break;
      if (i < 2) await new Promise(r => setTimeout(r, 100));
    }
    allBars.sort((a, b) => a.time - b.time);
    if (allBars.length > limit) allBars = allBars.slice(-limit);
    try {
      if (db) {
        await db.set(HIST_STORE, {
          key: cacheKey,
          timestamp: Date.now(),
          data: allBars.map((b) => ({
            t: b.time,
            o: b.open,
            h: b.high,
            l: b.low,
            c: b.close,
            v: b.volume,
          })),
        });
      }
    } catch (e) {
      console.warn("Failed to cache Bitstamp data:", e);
    }
    return allBars;
  }

  /**
   * Fetches historical data from Bybit with caching and progress updates.
   * @param {string} pair - Trading pair.
   * @param {number} interval - Time interval in seconds.
   * @param {number} limit - Maximum number of bars.
   * @param {Function} onProgress - Progress callback.
   * @param {AbortSignal} abortSignal - Signal to abort fetch.
   * @returns {Promise<Array>} Array of OHLCV bars.
   */
  async function fetchBybitHistoricalData(
    pair,
    interval = CONFIG.barInterval,
    limit = 6000,
    onProgress,
    abortSignal,
  ) {
    const cacheKey = `bybit_${pair}_historical_${interval}_${limit}_${Math.floor(Date.now() / CONFIG.cacheTTL)}`;
    let db;
    try {
      db = await getHistDb();
      const cached = await db.get(HIST_STORE, cacheKey);
      if (cached && cached.data.length >= limit * 0.9) {
        return cached.data.map((b) => ({
          time: b.t,
          open: b.o,
          high: b.h,
          low: b.l,
          close: b.c,
          volume: b.v,
        }));
      }
    } catch (e) {
      console.warn("Cache access failed, proceeding without cache:", e);
    }
    const symbol = `${pair}USDT`;
    let allBars = [],
      timeMap = new Map();
    const maxApiLimit = 1000;
    const intervalMap = {
      60: "1",
      300: "5",
      900: "15",
      1800: "30",
      3600: "60",
      14400: "240",
      86400: "D",
      D: "D",
      "1D": "D",
    };
    const bybitInterval = intervalMap[interval] || "5";
    const reverseIntervalMap = {
      1: 60,
      5: 300,
      15: 900,
      30: 1800,
      60: 3600,
      240: 14400,
      D: 86400,
    };
    const intervalSeconds = reverseIntervalMap[bybitInterval] || 300;
    const throttledProgress = onProgress
      ? utils.throttle(onProgress, 200)
      : null;
    const fetchBars = async (url) => {
      if (abortSignal?.aborted) throw new Error("Fetch aborted");
      const res = await fetch(url, { signal: abortSignal });
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      const data = await res.json();
      if (data.retCode !== 0) {
        console.error("Bybit API error:", data.retMsg);
        return 0;
      }
      let added = 0;
      if (data?.result?.list) {
        data.result.list.forEach((item) => {
          const t = Math.floor(parseInt(item[0]) / 1000);
          const nt = Math.floor(t / interval) * interval;
          if (!timeMap.has(nt)) {
            timeMap.set(nt, true);
            allBars.push({
              time: nt,
              open: parseFloat(item[1]),
              high: parseFloat(item[2]),
              low: parseFloat(item[3]),
              close: parseFloat(item[4]),
              volume: parseFloat(item[5]),
            });
            added++;
          }
        });
      }
      if (throttledProgress) throttledProgress(allBars.slice());
      return added;
    };
    try {
      let keepFetching = true;
      let fetchCount = 0;
      let lastEarliest = null;
      await fetchBars(
        `https://api.bybit.com/v5/market/kline?category=linear&symbol=${symbol}&interval=${bybitInterval}&limit=${maxApiLimit}`,
      );
      allBars.sort((a, b) => a.time - b.time);
      while (allBars.length < limit && keepFetching && allBars.length > 0) {
        allBars.sort((a, b) => a.time - b.time);
        const earliest = allBars[0].time;
        if (lastEarliest !== null && earliest >= lastEarliest) break;
        lastEarliest = earliest;
        const endTime = (earliest - 1) * 1000;
        const url = `https://api.bybit.com/v5/market/kline?category=linear&symbol=${symbol}&interval=${bybitInterval}&limit=${maxApiLimit}&end=${endTime}`;
        const added = await fetchBars(url).catch(() => 0);
        fetchCount++;
        if (added === 0) keepFetching = false;
        if (keepFetching && allBars.length < limit)
          await new Promise((resolve) => setTimeout(resolve, 100));
      }
      allBars.sort((a, b) => a.time - b.time);
      const result = allBars.length > limit ? allBars.slice(-limit) : allBars;
      if (result.length > 0) {
        let gaps = 0;
        for (let i = 1; i < result.length; i++) {
          const timeDiff = result[i].time - result[i - 1].time;
          if (timeDiff > intervalSeconds * 1.5) gaps++;
        }
        if (gaps > 0)
          console.warn(`Found ${gaps} data gaps in historical data`);
      }
      try {
        if (db) {
          await db.set(HIST_STORE, {
            key: cacheKey,
            timestamp: Date.now(),
            data: result.map((b) => ({
              t: b.time,
              o: b.open,
              h: b.high,
              l: b.low,
              c: b.close,
              v: b.volume,
            })),
          });
        }
      } catch (e) {
        console.warn("Failed to cache Bybit data:", e);
      }
      return result;
    } catch (e) {
      console.error("Bybit historical data fetch failed:", e);
      return [];
    }
  }

  window.fetchBybitHistoricalData = fetchBybitHistoricalData;

  const preCalculateDataCache = new Map();

  /**
   * Pre-calculates data for a trading pair.
   * @param {string} pair - Trading pair.
   * @param {HTMLElement} overlay - Overlay element for UI feedback.
   * @param {Function} progressiveUpdate - Progress callback.
   * @param {AbortSignal} abortSignal - Signal to abort fetch.
   * @returns {Promise<Object|null>} Calculated data or null on error.
   */
  async function preCalculateData(
    pair,
    overlay,
    progressiveUpdate,
    abortSignal,
  ) {
    const cacheKey = `${pair}_${CONFIG.barInterval}`;
    if (preCalculateDataCache.has(cacheKey))
      return preCalculateDataCache.get(cacheKey);
    try {
      const timeoutDuration = window.CONFIG?.ui?.loadingTimeout || 15000;
      const fetchTimeout = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error(`Timeout fetching data for ${pair}`)),
          timeoutDuration,
        ),
      );
      const bitstampPair = `${pair.toLowerCase()}usd`;
      const [rawPriceData, bybitData] = await Promise.all([
        Promise.race([
          fetchBitstampHistoricalData(
            bitstampPair,
            CONFIG.barInterval,
            undefined,
            abortSignal,
          ),
          fetchTimeout,
        ]),
        Promise.race([
          fetchBybitHistoricalData(
            pair,
            CONFIG.barInterval,
            CONFIG.maxBars,
            (bars) => {
              progressiveBybitBars = bars;
              if (typeof progressiveUpdate === "function")
                progressiveUpdate(bars);
            },
            abortSignal,
          ),
          fetchTimeout,
        ]),
      ]);
      const priceData = utils.filterValidBars(rawPriceData || []);
      const cleanBybitData = utils.filterValidBars(bybitData || []);
      if (!priceData.length) return null;
      const indicatorResults =
        window.chartIndicators.calculateAllIndicators(priceData);
      const allTimes = [
        ...new Set([
          ...(cleanBybitData.map((d) => d.time) || []),
          ...priceData.map((d) => d.time),
        ]),
      ].sort();
      const bybitMap = new Map(cleanBybitData.map((d) => [d.time, d]));
      const bitstampMap = new Map(priceData.map((d) => [d.time, d]));
      let lastBybitClose = bybitData[0]?.close || 0;
      let lastBitstampClose = priceData[0]?.close || 0;
      const aligned = allTimes.map((time) => {
        const bybit = bybitMap.get(time) || {
          time,
          open: lastBybitClose,
          high: lastBybitClose,
          low: lastBybitClose,
          close: lastBybitClose,
          volume: 0,
        };
        const bitstamp = bitstampMap.get(time) || {
          time,
          open: lastBitstampClose,
          high: lastBitstampClose,
          low: lastBitstampClose,
          close: lastBitstampClose,
          volume: 0,
        };
        lastBybitClose = bybit.close;
        lastBitstampClose = bitstamp.close;
        return { time, bybit, bitstamp };
      });
      const alignedBybit = aligned.map((d) => d.bybit);
      const alignedBitstamp = aligned.map((d) => d.bitstamp);
      const { liqsData, liqsRaw, perpD, spotD } =
        window.chartIndicators.calculateLiqs(
          alignedBybit,
          alignedBitstamp,
          CONFIG.sdPeriod,
        );
      const openInterestData = alignedBybit.map((b) => ({
        time: b.time,
        price: b.close,
        close: b.close,
        openInterest: b.volume * 10,
        priceChange: (b.close - b.open) / b.open,
        fundingRate: 0,
        buyFlow: b.volume * 0.6,
        sellFlow: b.volume * 0.4,
        hasOrderFlow: true,
      }));
      const result = {
        priceData,
        bands: indicatorResults.bands,
        vwap: indicatorResults.vwap,
        vwapData: indicatorResults.vwapData,
        emaBands: indicatorResults.emaBands,
        caches: indicatorResults.caches,
        liqsData,
        liqsRawWindow: liqsRaw.slice(-CONFIG.sdPeriod),
        sums: {
          perpSum: perpD[perpD.length - 1].value,
          spotSum: spotD[spotD.length - 1].value,
        },
        alignedBybit,
        alignedBitstamp,
        openInterestData,
        timing: {
          firstTime: allTimes[0],
          lastTime: allTimes[allTimes.length - 1],
        },
      };
      preCalculateDataCache.set(cacheKey, result);
      return result;
    } catch (e) {
      handleError(`Error pre-calculating data for ${pair}`, e, overlay);
      return null;
    }
  }

  /**
   * Initializes the chart and meter for a trading pair.
   * @param {HTMLElement} container - Chart container element.
   * @param {Object} data - Pre-calculated data.
   * @param {string} pair - Trading pair.
   * @param {Function} progressiveLoader - Progressive loading callback.
   * @returns {Object|null} Chart state or null on error.
   */
  function initializeChartAndMeter(container, data, pair, progressiveLoader) {
    const overlay = domCache.get("overlay");
    let userHasScrolled = false;
    const filteredPriceData = utils.filterValidBars(data?.priceData);
    if (!data || !filteredPriceData.length) {
      overlay.textContent = `Failed to load ${pair} data (no price data)`;
      overlay.style.display = "block";
      console.error(
        `[initializeChartAndMeter] No price data for ${pair}:`,
        data,
      );
      return null;
    }
    const priceChartContainer = domCache.get("priceChartContainer");
    if (priceChartContainer) priceChartContainer.style.height = "100%";
    const chartConfig = {
      ...CONFIG,
      ticker: {
        symbol: pair,
        bitstampOrderBook: `order_book_${pair.toLowerCase()}usd`,
        bitstampTrades: `live_trades_${pair.toLowerCase()}usd`,
        bybitTrades: `publicTrade.${pair.toUpperCase()}USDT`,
      },
    };
    let priceChartElement = domCache.get("priceChartElement");
    if (!priceChartElement) {
      priceChartElement = container.querySelector(".price-chart");
      domCache.set("priceChartElement", priceChartElement);
    }
    if (!priceChartElement) {
      overlay.textContent = `Chart container missing for ${pair}`;
      overlay.style.display = "block";
      console.error(
        `[initializeChartAndMeter] .price-chart element not found for ${pair}`,
      );
      return null;
    }
    let priceChart;
    try {
      const chartColors = window.CONFIG?.chart?.defaultColors || {
        background: "#0f141a",
        text: "#D3D3D3",
        grid: "#2A2A2A",
      };
      priceChart = LightweightCharts.createChart(priceChartElement, {
        autoSize: true,
        layout: {
          background: { color: chartColors.background, type: "solid" },
          textColor: chartColors.text,
          fontSize: 10,
          attributionLogo: false,
        },
        panes: [{}, { height: 150, visible: true }],
        grid: { vertLines: { visible: false }, horzLines: { visible: false } }, // Grid lines could also be configured
        crosshair: {
          mode: LightweightCharts.CrosshairMode.Normal,
          vertLine: { labelBackgroundColor: chartColors.grid },
          horzLine: { labelBackgroundColor: chartColors.grid },
        },
        timeScale: {
          timeVisible: true,
          secondsVisible: false,
          borderColor: chartColors.grid,
          lockVisibleTimeRangeOnResize: true,
          fixLeftEdge: false,
          fixRightEdge: false,
          kineticScroll: { touch: true, mouse: false },
          tickMarkFormatter: (t) =>
            new Date(t * 1000).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            }),
        },
        rightPriceScale: {
          borderColor: chartColors.grid,
          autoScale: true,
          entireTextOnly: false,
        },
        trackingMode: {
          exitMode: LightweightCharts.TrackingModeExitMode.OnNextTap,
        },
        handleScale: { axisPressedMouseMove: { time: true, price: true } },
        handleScroll: { vertTouchDrag: true, horzTouchDrag: true },
      });
    } catch (err) {
      overlay.textContent = `Chart initialization failed for ${pair}`;
      overlay.style.display = "block";
      console.error(
        `[initializeChartAndMeter] Chart creation failed for ${pair}:`,
        err,
        data,
      );
      return null;
    }
    priceChart.subscribeCrosshairMove((param) => {
      priceChart.applyOptions({
        crosshair: {
          mode: param.point
            ? LightweightCharts.CrosshairMode.Normal
            : LightweightCharts.CrosshairMode.Hidden,
        },
      });
    });
    const chartExtras = { watermark: null, upDownMarkers: null };
    if (window.LightweightCharts?.createTextWatermark) {
      const panes = priceChart.panes();
      if (panes?.length > 0)
        chartExtras.watermark = window.LightweightCharts.createTextWatermark(
          panes[0],
          {
            horzAlign: "right",
            vertAlign: "bottom",
            lines: [
              {
                text: `${pair.toUpperCase()}USD`,
                color: "rgba(255, 255, 255, 0.3)",
                fontSize: 28,
                fontStyle: "bold",
                fontFamily: "Arial",
              },
            ],
            padding: { right: 28 },
          },
        );
    }
    const candleColors = window.CONFIG?.chart?.candlestick || {
      upColor: "#AAAAAA",
      downColor: "#AAAAAA",
      borderColor: "#AAAAAA",
      wickUpColor: "#AAAAAA",
      wickDownColor: "#AAAAAA",
    };
    const priceSeries = priceChart.addSeries(
      LightweightCharts.CandlestickSeries,
      {
        ...candleColors,
        lastValueVisible: true,
        priceLineVisible: true,
        priceLineSource: LightweightCharts.PriceLineSource.LastBar,
        priceFormat: { type: "price", precision: 2, minMove: 0.01 },
      },
    );
    const effectiveData = data || {
      priceData: [],
      bands: { t1: 0, t2: 0, b1: 0, b2: 0, time: 0 },
      vwap: {
        vwapValue: 0,
        upperBand: 0,
        lowerBand: 0,
        upperMidline: 0,
        lowerMidline: 0,
      },
      emaBands: { ema: 0, upper: 0, lower: 0, time: 0 },
      liqsData: [{ time: Math.floor(Date.now() / 1000), value: 0 }],
      liqsRawWindow: [],
      sums: { perpSum: 0, spotSum: 0 },
      alignedBybit: [],
      alignedBitstamp: [],
      timing: {
        firstTime: Math.floor(Date.now() / 1000),
        lastTime: Math.floor(Date.now() / 1000),
      },
    };
    const createPriceLine = (price, title) => {
      if (!Number.isFinite(price)) {
        console.warn(
          `[initializeChartAndMeter] Skipping price line for ${title} due to invalid price:`,
          price,
        );
        return null;
      }
      const lineColors = window.CONFIG?.chart?.defaultColors || {
        grid: "#2A2A2A",
      }; // Using grid color for price lines as a default
      return priceSeries.createPriceLine({
        price,
        color: lineColors.grid,
        lineWidth: 1,
        title,
      }); // Changed #555555 to use config
    };
    const priceLines = {
      b2Upper: createPriceLine(effectiveData.bands.t2, "2σMR"),
      b1Upper: createPriceLine(effectiveData.bands.t1, "1σMR"),
      b1Lower: createPriceLine(effectiveData.bands.b1, "1σMR"),
      b2Lower: createPriceLine(effectiveData.bands.b2, "2σMR"),
      stdPlus2: createPriceLine(effectiveData.vwap.upperBand, "std+2"),
      stdPlus1: createPriceLine(effectiveData.vwap.upperMidline, "std+1"),
      vwap: createPriceLine(effectiveData.vwap.vwapValue, "vwap"),
      stdMinus1: createPriceLine(effectiveData.vwap.lowerMidline, "std-1"),
      stdMinus2: createPriceLine(effectiveData.vwap.lowerBand, "std-2"),
    };
    requestAnimationFrame(() => {
      try {
        priceSeries.setData(utils.filterValidBars(effectiveData.priceData));
      } catch (err) {
        overlay.textContent = `Failed to set chart data for ${pair}`;
        overlay.style.display = "block";
        console.error(
          `[initializeChartAndMeter] setData failed for ${pair}:`,
          err,
          effectiveData.priceData,
        );
      }
    });
    if (window.LightweightCharts?.createUpDownMarkers) {
      const markerSeries = priceChart.addSeries(LightweightCharts.LineSeries, {
        lineWidth: 0,
        lastValueVisible: false,
        priceLineVisible: false,
        crosshairMarkerVisible: false,
        visible: false,
        title: "",
      });
      markerSeries.setData(
        utils
          .filterValidBars(effectiveData.priceData)
          .map((b) => ({ time: b.time, value: b.close })),
      );
      const markerConfig = window.CONFIG?.chart?.upDownMarkers || {
        threshold: 0.005,
        upColor: "rgba(0, 255, 255, 0.7)",
        downColor: "rgba(255, 85, 85, 0.7)",
        size: 0.5,
      };
      chartExtras.upDownMarkers = window.LightweightCharts.createUpDownMarkers(
        markerSeries,
        markerConfig,
      );
      chartExtras.markerSeries = markerSeries;
    }
    if (typeof progressiveLoader === "function") {
      const guardedLoader = (series) => {
        progressiveLoader({
          update: (bar) => {
            if (
              typeof versionToken !== "undefined" &&
              state &&
              state._versionToken !== versionToken
            )
              return;
            const validBar = utils.filterValidBars([bar])[0];
            if (validBar) {
              series.update(validBar);
              if (chartExtras.markerSeries)
                chartExtras.markerSeries.update({
                  time: validBar.time,
                  value: validBar.close,
                });
            }
          },
          setData: (bars) => series.setData(utils.filterValidBars(bars)),
        });
      };
      guardedLoader(priceSeries);
    }
    const timeRange = effectiveData.timing.lastTime - effectiveData.timing.firstTime + CONFIG.barInterval * CONFIG.futureBars;
    const midPoint = effectiveData.timing.lastTime - timeRange / 4;
    let cvdComponents = null;
    if (window.cvdModule?.createCVDChart) {
      cvdComponents = window.cvdModule.createCVDChart(container, priceChart);
      priceChart.applyOptions({
        layout: {
          background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        },
      });
      const syncResources = window.cvdModule.synchronizeCharts(
        cvdComponents,
        priceChart,
      );
      cvdComponents.syncResources = syncResources;
      window.cvdModule.initializeCVDData(
        cvdComponents,
        utils.filterValidBars(effectiveData.priceData),
      );
      window.cvdModule.setupCVDUpdateInterval(cvdComponents);
    }
    let perpCvdComponents = null;
    if (window.perpCvdModule?.createCVDChart) {
      perpCvdComponents = window.perpCvdModule.createCVDChart(
        container,
        priceChart,
      );
      const perpSyncResources = window.perpCvdModule.synchronizeCharts(
        perpCvdComponents,
        priceChart,
      );
      perpCvdComponents.syncResources = perpSyncResources;
      const bybitBars = utils.filterValidBars(effectiveData.alignedBybit);
      window.perpCvdModule.initializeCVDData(perpCvdComponents, bybitBars);
      window.perpCvdModule.setupCVDUpdateInterval(perpCvdComponents);
    }
    let perpImbalanceComponents = null;
    if (window.perpImbalance?.createPerpImbalanceIndicator) {
      perpImbalanceComponents =
        window.perpImbalance.createPerpImbalanceIndicator(priceChart);
      const bitstampBars = utils.filterValidBars(effectiveData.alignedBitstamp);
      const bybitBars = utils.filterValidBars(effectiveData.alignedBybit);
      window.perpImbalance.initializeImbalanceData(
        perpImbalanceComponents,
        bitstampBars,
        bybitBars,
        effectiveData.openInterestData,
      );
      const syncResources = window.perpImbalance.synchronizeCharts(
        perpImbalanceComponents,
        priceChart,
      );
      perpImbalanceComponents.syncResources = syncResources;
      window.perpImbalance.setupPerpUpdateInterval?.(perpImbalanceComponents);
    }
    requestAnimationFrame(() => {
      priceChart.timeScale().fitContent();
      if (Number.isFinite(midPoint) && Number.isFinite(timeRange)) {
        priceChart.timeScale().setVisibleRange({
          from: midPoint - timeRange / 4,
          to: midPoint + timeRange / 4,
        });
      }

      // Apply savedVisibleLogicalRange if it exists
      try {
        const currentChartState = window.chartStates.get(pair);
        if (
          currentChartState &&
          currentChartState.savedVisibleLogicalRange &&
          typeof currentChartState.savedVisibleLogicalRange.from === "number" &&
          typeof currentChartState.savedVisibleLogicalRange.to === "number"
        ) {
          priceChart
            .timeScale()
            .setVisibleLogicalRange(currentChartState.savedVisibleLogicalRange);
          currentChartState.savedVisibleLogicalRange = null;
        }
      } catch (e) {
        console.warn(
          `[Charts-${pair}] Error applying saved visibleLogicalRange:`,
          e.message,
        );
      }
    });
    priceChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
      const logicalRange = priceChart.timeScale().getVisibleLogicalRange();
      const bars = state?.data?.priceData || [];
      if (logicalRange && bars.length > 0) {
        const lastBarTime = bars[bars.length - 1].time;
        userHasScrolled = logicalRange.to < lastBarTime - 0.5;
      }
    });
    const state = {
      chart: {
        priceChart,
        priceSeries,
        cvdComponents,
        perpCvdComponents,
        perpImbalanceComponents,
        priceLines,
        extras: chartExtras,
      },
      config: chartConfig,
      data: {
        priceData: filteredPriceData,
        alignedBybit: utils.filterValidBars(effectiveData.alignedBybit),
        alignedBitstamp: utils.filterValidBars(effectiveData.alignedBitstamp),
        openInterestData: effectiveData.openInterestData || [],
        orderBook: { bids: [], asks: [] },
        liquidationsData: [],
      },
      caches: effectiveData.caches || {
        twapCache: { priceVolume: 0, totalVolume: 0, value: 0 },
        vwapCache: { priceVolume: 0, totalVolume: 0, anchor: null },
        stdDevCache: {},
        vwapActive: false,
      },
      sums: effectiveData.sums || { perpSum: 0, spotSum: 0 },
      liqs: { liqsRawWindow: effectiveData.liqsRawWindow || [] },
      timing: {
        firstTime: effectiveData.timing.firstTime,
        lastTime: effectiveData.timing.lastTime,
        lastPriceUpdateTime: effectiveData.timing.lastTime,
      },
      currentBars: {
        currentBarBitstamp: filteredPriceData[filteredPriceData.length - 1]
          ? { ...filteredPriceData[filteredPriceData.length - 1] }
          : {
              time:
                Math.floor(Date.now() / 1000 / CONFIG.barInterval) *
                CONFIG.barInterval,
              open: null,
              high: null,
              low: null,
              close: null,
              volume: 0,
            },
        currentBarBybit: utils.filterValidBars(effectiveData.alignedBybit)[
          utils.filterValidBars(effectiveData.alignedBybit).length - 1
        ]
          ? {
              ...utils.filterValidBars(effectiveData.alignedBybit)[
                utils.filterValidBars(effectiveData.alignedBybit).length - 1
              ],
            }
          : {
              time:
                Math.floor(Date.now() / 1000 / CONFIG.barInterval) *
                CONFIG.barInterval,
              open: null,
              high: null,
              low: null,
              close: null,
              volume: 0,
            },
      },
      chartExtras,

      isActive: true,
      throttledFunctions: {
        throttledPriceUpdate: (bar) => {
          if (
            !state.isActive ||
            !bar ||
            !Number.isFinite(bar.time) ||
            !Number.isFinite(bar.close)
          )
            return;
          const validBar = utils.filterValidBars([bar])[0];
          if (!validBar) return;
          const lastBar = state.data.priceData[state.data.priceData.length - 1];
          if (!lastBar || validBar.time >= lastBar.time) {
            if (!lastBar || validBar.time > lastBar.time) {
              state.data.priceData.push(validBar);
              if (state.data.priceData.length > CONFIG.maxBars)
                state.data.priceData.shift();
              // Inform CVD data store about the updated price data
              if (
                window.PS &&
                typeof window.PS.setCVDPriceData === "function"
              ) {
                window.PS.setCVDPriceData(state.data.priceData);
              }
              requestAnimationFrame(() => {
                priceSeries.update(validBar);
                if (state.chart.extras?.markerSeries)
                  state.chart.extras.markerSeries.update({
                    time: validBar.time,
                    value: validBar.close,
                  });
                let prevBar =
                  state.data.priceData[state.data.priceData.length - 2] ||
                  state.data.priceData.find((b) => Number.isFinite(b.close));
                if (state.chart.cvdComponents && window.cvdModule?.updateCVD) {
                  let lastCvdValue =
                    state.chart.cvdComponents?.series
                      ?.dataByIndex?.()
                      ?.slice(-1)[0]?.value || 0;
                  window.cvdModule.updateCVD(
                    state.chart.cvdComponents,
                    validBar,
                    prevBar,
                    lastCvdValue,
                  );
                }
                // PerpCVD now uses subscription-only updates to maintain "1 bar back" behavior like PerpImbalance
                // Real-time updateCVD call removed to fix current bar vs 1 bar back alignment issue
                if (
                  window.perpImbalance?.updateImbalance &&
                  state.chart.perpImbalanceComponents
                ) {
                  const spotBar = bar;
                  let futuresBar =
                    state.currentBars.currentBarBybit.time === bar.time
                      ? state.currentBars.currentBarBybit
                      : state.data.alignedBybit.find(
                          (b) => b.time === bar.time,
                        );
                  let oiBar =
                    state.data.openInterestData?.find(
                      (b) => b.time === bar.time,
                    ) || null;
                  if (spotBar && futuresBar)
                    window.perpImbalance.updateImbalance(
                      state.chart.perpImbalanceComponents,
                      spotBar,
                      futuresBar,
                      oiBar,
                    );
                }
                if (state.liquidationManager?.checkForCandleClose) {
                  state.liquidationManager.checkForCandleClose(bar.time * 1000);
                  if (
                    state.chart.cvdComponents &&
                    window.cvdModule?.renderPendingCVDUpdates
                  ) {
                    window.cvdModule.renderPendingCVDUpdates(
                      state.chart.cvdComponents,
                    );
                  }
                  if (
                    state.chart.perpCvdComponents &&
                    window.perpCvdModule?.renderPendingCVDUpdates
                  ) {
                    window.perpCvdModule.renderPendingCVDUpdates(
                      state.chart.perpCvdComponents,
                    );
                  }
                  if (
                    state.chart.perpImbalanceComponents &&
                    window.perpImbalance?.renderPendingUpdates
                  ) {
                    window.perpImbalance.renderPendingUpdates(
                      state.chart.perpImbalanceComponents,
                    );
                  }
                }
                window.deltaOiProfileManager?.updateProfile?.(
                  state.config?.ticker?.symbol,
                ) ||
                  state.deltaOiProfile?.update?.() ||
                  (() => {});
                if (!userHasScrolled) {
                }
              });
              if (Math.random() < 0.1)
                memoryManagement.cleanupHistoricalData(state);
            }
          }
        },
        throttledCloseUpdate: (bar) => {
          if (
            !state.isActive ||
            !bar ||
            !Number.isFinite(bar.time) ||
            !Number.isFinite(bar.close)
          )
            return;
          const validBar = utils.filterValidBars([bar])[0];
          if (!validBar) return;
          const lastBar = state.data.priceData[state.data.priceData.length - 1];
          if (lastBar && validBar.time === lastBar.time) {
            lastBar.close = validBar.close;
            requestAnimationFrame(() => {
              priceSeries.update(lastBar);
              if (state.chart.extras?.markerSeries)
                state.chart.extras.markerSeries.update({
                  time: validBar.time,
                  value: validBar.close,
                });
              if (state.liquidationManager?.checkForCandleClose) {
                state.liquidationManager.checkForCandleClose(Date.now());
                if (
                  state.chart.cvdComponents &&
                  window.cvdModule?.renderPendingCVDUpdates
                ) {
                  window.cvdModule.renderPendingCVDUpdates(
                    state.chart.cvdComponents,
                  );
                }
              }
            });
          }
        },
        updateOrderBook: (state) =>
          chartOrderbook?.updateOrderBookLines?.(state),

        updateEMABands: () => {
          if (
            !state.isActive ||
            !state.data.priceData.length ||
            !window.chartIndicators?.calculateEMABands
          )
            return;
          const emaBands = window.chartIndicators.calculateEMABands(
            state.data.priceData,
          );
          state.data.emaBands = emaBands;
          if (state.chart.priceLines.ema) {
            state.chart.priceLines.ema.applyOptions({ price: emaBands.ema });
            state.chart.priceLines.emaUpper.applyOptions({
              price: emaBands.upper,
            });
            state.chart.priceLines.emaLower.applyOptions({
              price: emaBands.lower,
            });
          }
        },
      },
      largeOrderLines: [],
    };
    // Create manager template to reduce duplication
    const createManager = (type) => ({
      [`add${type}`]: () => {},
      isActiveFn: () => false,
      checkForCandleClose: () => {},
      cleanup: () => {},
      destroy: () => {},
      ...(type === 'Liquidation' && { processLiquidation: () => {} }),
      dollarThreshold: parseFloat(localStorage.getItem(`${type.toLowerCase()}Threshold`)) || 100000,
    });

    state.liquidationManager = createManager('Liquidation');
    state.whaleAlertManager = createManager('WhaleAlert');
    state.chartContainer =
      domCache.get("priceChartElement") ||
      container.querySelector(".price-chart");
    function tryInitDeltaOiProfile(retries = 5, delay = 200) {
      if (state.config && state.config.ticker && state.config.ticker.symbol) {
        window.deltaOiProfileManager?.initializeProfiles?.(state);

        // Consolidated initialization with single timeout
        setTimeout(() => {
          const lc = document.getElementById("liquidation-console");
          if (lc) lc.style.cssText = "display: block; visibility: visible; opacity: 1";

          window.deltaOiProfileManager?.updateProfile?.(state.config?.ticker?.symbol) ||
            state.deltaOiProfile?.update?.() ||
            (() => {});
          window.updateSize();
        }, 100);
      } else if (retries > 0) {
        setTimeout(() => tryInitDeltaOiProfile(retries - 1, delay), delay);
      } else {
        console.warn("DeltaOiProfile: ticker not set after retries, skipping profile initialization.");
      }
    }
    tryInitDeltaOiProfile();
    if (perpImbalanceComponents) {
      state.chart.perpImbalanceComponents = perpImbalanceComponents;
      window.perpImbalance?.disableCrosshairMarkers?.(perpImbalanceComponents);
    }
    requestAnimationFrame(() => (overlay.style.display = "none"));
    return state;
  }

  const messageQueue = {
    add: (source, data) => {
      const state = chartStates.get(currentPair);
      if (state) handleWebSocketMessage(data, source, state);
    },
  };

  /**
   * Handles WebSocket messages and updates chart state.
   * @param {Object} message - WebSocket message data.
   * @param {string} source - Source of the message (e.g., 'bybit', 'bitstamp').
   * @param {Object} chartState - Current chart state.
   */
  function handleWebSocketMessage(message, source, chartState) {
    if (!chartState?.config?.ticker) return;
    const pair = chartState.config.ticker.symbol;
    try {
      if (
        source === "bybit" &&
        message.topic?.startsWith("publicTrade.") &&
        message.data
      ) {
        const trades = Array.isArray(message.data)
          ? message.data
          : [message.data];
        requestAnimationFrame(() => {
          trades.forEach((trade) => {
            if (trade && trade.S && trade.p && trade.v) {
              window.PS &&
                window.PS.addBybitTrade &&
                window.PS.addBybitTrade({
                  side: trade.S,
                  price: parseFloat(trade.p),
                  size: parseFloat(trade.v),
                });
            }
          });
        });
      }
      if (
        source === "bybit" &&
        message.topic?.startsWith("liquidation.") &&
        message.data
      ) {
        if (!window.bybitWsManager?.isConnected()) {
          console.warn("Received Bybit message but connection is not healthy");
          return;
        }
        const liquidations = Array.isArray(message.data)
          ? message.data
          : [message.data];
        const batch = liquidations
          .map((liq) => ({
            price: parseFloat(liq.price),
            amount: parseFloat(liq.size || liq.qty),
            side: liq.side?.toLowerCase(),
            value: parseFloat(liq.price) * parseFloat(liq.size || liq.qty),
          }))
          .filter(
            (l) =>
              Number.isFinite(l.price) && Number.isFinite(l.amount) && l.side,
          );
        requestAnimationFrame(() => {
          batch.forEach((liq) => {
            if (window.liqEventCallback) {
              window.liqEventCallback({
                pair,
                price: liq.price,
                size: liq.amount,
                side: liq.side,
                value: liq.value,
                timestamp: new Date().toISOString(),
              });
            }
          });
        });
      }
      if (source === "bitstamp") {
        if (
          message.channel === `live_trades_${pair.toLowerCase()}usd` &&
          message.data &&
          Number.isFinite(message.data.price) &&
          Number.isFinite(message.data.amount)
        ) {
          const price = parseFloat(message.data.price);
          const volume = parseFloat(message.data.amount);
          const type = message.data.type;
          document.title = `${pair} $${price.toFixed(2)} | Crypto Dashboard`;
          directUpdatePriceData(chartState, price, volume, type);
        }
        if (
          message.channel === `order_book_${pair.toLowerCase()}usd` &&
          message.data?.bids &&
          message.data?.asks &&
          chartState.data?.orderBook
        ) {
          chartState.data.orderBook.bids = message.data.bids.map(([p, v]) => [
            parseFloat(p),
            parseFloat(v),
          ]);
          chartState.data.orderBook.asks = message.data.asks.map(([p, v]) => [
            parseFloat(p),
            parseFloat(v),
          ]);
          if (chartOrderbook?.updateOrderBookLines)
            chartOrderbook.updateOrderBookLines(chartState);
          else
            console.warn(
              "chartOrderbook is not defined, skipping order book update",
            );
        }
        if (message.error || message.reason)
          console.error(
            "Bitstamp error message:",
            message.error || message.reason,
            message,
          );
      } else if (
        source === "bybit" &&
        message.topic?.startsWith("liquidation.") &&
        chartState.liquidationManager &&
        message.data
      ) {
        const liquidations = Array.isArray(message.data)
          ? message.data
          : [message.data];
        const batch = liquidations
          .map((liq) => ({
            time: parseInt(liq.time) / 1000,
            price: parseFloat(liq.price),
            amount: parseFloat(liq.size || liq.qty),
            side: liq.side?.toLowerCase(),
            type: liq.side.toLowerCase() === "buy" ? 0 : 1,
          }))
          .filter(
            (l) =>
              Number.isFinite(l.time) &&
              Number.isFinite(l.price) &&
              Number.isFinite(l.amount) &&
              l.side,
          );
        requestAnimationFrame(() => {
          batch.forEach((liq) => {
            if (chartState.data.liquidationsData) {
              chartState.data.liquidationsData.push({
                time: Math.floor(Date.now() / 1000),
                price: liq.price,
                amount: liq.amount,
                side: liq.side,
              });
              if (
                chartState.data.liquidationsData.length >
                chartState.config.maxBars
              )
                chartState.data.liquidationsData =
                  chartState.data.liquidationsData.slice(
                    -chartState.config.maxBars,
                  );
              chartState.deltaOiProfile?.update?.();
            }
            const signedVolume = liq.type === 0 ? liq.amount : -liq.amount;
            if (!chartState.metrics)
              chartState.metrics = {
                buyVolume: 0,
                sellVolume: 0,
                buyValue: 0,
                sellValue: 0,
                liquidations: 0,
                liquidationsMin: 0,
                liquidationsMax: 0,
                spotPressure: 0,
              };
            chartState.metrics.liquidations += signedVolume;
            chartState.metrics.liquidationsMin = Math.min(
              chartState.metrics.liquidationsMin,
              chartState.metrics.liquidations,
            );
            chartState.metrics.liquidationsMax = Math.max(
              chartState.metrics.liquidationsMax,
              chartState.metrics.liquidations,
            );
            chartState.throttledFunctions.updateMetrics?.();
          });
        });
      }
      const now2 = new Date();
      if (
        now2.getUTCDay() === 1 &&
        now2.getUTCHours() === 13 &&
        now2.getUTCMinutes() === 30 &&
        !chartState._nySessionRefreshed
      ) {
        chartState._nySessionRefreshed = true;
        requestAnimationFrame(() => {
          if (chartState.isActive && chartState.data.priceData.length) {
            const vwapResults = window.chartIndicators.calculateAllIndicators(
              chartState.data.priceData,
            );
            chartState.data.vwap = vwapResults.vwap;
            chartState.data.vwapData = vwapResults.vwapData;
            chartState.caches = vwapResults.caches;
            if (chartState.chart.priceLines.vwap)
              chartState.chart.priceLines.vwap.applyOptions({
                price: vwapResults.vwap.vwapValue,
              });
            if (chartState.chart.priceLines.stdPlus2)
              chartState.chart.priceLines.stdPlus2.applyOptions({
                price: vwapResults.vwap.upperBand,
              });
            if (chartState.chart.priceLines.stdMinus2)
              chartState.chart.priceLines.stdMinus2.applyOptions({
                price: vwapResults.vwap.lowerBand,
              });
            if (chartState.chart.priceLines.stdPlus1)
              chartState.chart.priceLines.stdPlus1.applyOptions({
                price: vwapResults.vwap.upperMidline,
              });
            if (chartState.chart.priceLines.stdMinus1)
              chartState.chart.priceLines.stdMinus1.applyOptions({
                price: vwapResults.vwap.lowerMidline,
              });
          }
        });
        setTimeout(
          () => (chartState._nySessionRefreshed = false),
          (24 - now2.getUTCDay()) * 3600000,
        );
      }
    } catch (e) {
      handleError(`WebSocket handler error (${source})`, e);
    }
  }

  /**
   * Subscribes to WebSocket channels for a trading pair.
   * @param {string} pair - Trading pair.
   */
  function subscribePair(pair) {
    const state = chartStates.get(pair);
    if (state?.isSubscribed) return;
    const lp = pair.toLowerCase();
    const channels = [
      {
        m: window.bitstampWsManager,
        c: `order_book_${lp}usd`,
        h: (d) => messageQueue.add("bitstamp", d),
      },
      {
        m: window.bitstampWsManager,
        c: `live_trades_${lp}usd`,
        h: (d) => messageQueue.add("bitstamp", d),
      },
      {
        m: window.bybitWsManager,
        c: `liquidation.${pair.toUpperCase()}USDT`,
        h: (d) => {
          if (!d?.data) return;
          const liqs = Array.isArray(d.data) ? d.data : [d.data];
          liqs.forEach((l) => {
            if (!l?.price || !l.side) return;
            const p = parseFloat(l.price);
            const a = parseFloat(l.size || l.qty || 0);
            const s = l.side.toLowerCase();
            if (Number.isFinite(p) && Number.isFinite(a) && a > 0)
              window.eventBus.publish(`liquidation-${pair}`, {
                price: p,
                amount: a,
                side: s,
                timestamp: Math.floor(Date.now() / 1000),
              });
          });
        },
      },
      {
        m: window.bybitWsManager,
        c: `publicTrade.${pair.toUpperCase()}USDT`,
        h: (d) => messageQueue.add("bybit", d),
      },
      // After subscribing to Bybit trade/liquidation, also subscribe to Bybit kline for perpCVD/perpImbalance
      {
        m: window.bybitWsManager,
        c: `kline.5.${pair.toUpperCase()}USDT`,
        h: (message) => {
          if (message.topic && message.topic.startsWith("kline.5.")) {
            // Bybit kline message structure: { topic: 'kline.5.BTCUSDT', data: [{ ... }] }
            const klines = Array.isArray(message.data)
              ? message.data
              : [message.data];
            klines.forEach((kline) => {
              // Parse kline bar
              const bar = {
                time: Math.floor(Number(kline.start) / 1000),
                open: parseFloat(kline.open),
                high: parseFloat(kline.high),
                low: parseFloat(kline.low),
                close: parseFloat(kline.close),
                volume: parseFloat(kline.volume),
              };
              // Update alignedBybit array (if present)
              const state = chartStates.get(pair);
              if (
                state &&
                state.data &&
                Array.isArray(state.data.alignedBybit)
              ) {
                // Only add if new bar
                const lastBar =
                  state.data.alignedBybit[state.data.alignedBybit.length - 1];
                if (!lastBar || lastBar.time < bar.time) {
                  state.data.alignedBybit.push(bar);
                  if (state.data.alignedBybit.length > CONFIG.maxBars)
                    state.data.alignedBybit.shift();
                  // Trigger data store updates to notify WebSocket subscriptions
                  if (window.PS && window.PS.setPerpCVDPriceData) {
                    window.PS.setPerpCVDPriceData(
                      state.data.alignedBybit.slice(),
                    );
                  }

                  // Update PerpImbalance data store with spot and futures data
                  if (window.PS && window.PS.setPerpImbalanceSourceData) {
                    const spotBars = state.data.priceData;
                    window.PS.setPerpImbalanceSourceData({
                      spot: spotBars,
                      futures: state.data.alignedBybit.slice(),
                      oi: state.data.openInterestData || [],
                    });
                  }

                  // PerpImbalance: Only update after bar close with closed bar data (like CVD)
                  if (
                    state.chart &&
                    state.chart.perpImbalanceComponents &&
                    window.perpImbalance?.updateImbalance
                  ) {
                    // Use "1 bar back" logic like CVD - only use closed bars for calculation
                    const spotBars = state.data.priceData;
                    const futuresBars = state.data.alignedBybit;

                    if (futuresBars.length >= 2 && spotBars.length >= 2) {
                      // Get the previous closed bar (1 bar back)
                      const closedFuturesBar =
                        futuresBars[futuresBars.length - 2];

                      // Find matching closed spot bar
                      const closedSpotBar =
                        spotBars
                          .slice()
                          .reverse()
                          .find((b) => b.time <= closedFuturesBar.time) ||
                        spotBars[spotBars.length - 2];

                      // Find matching OI bar
                      let oiBar = null;
                      if (Array.isArray(state.data.openInterestData)) {
                        oiBar =
                          state.data.openInterestData.find(
                            (b) => b.time === closedFuturesBar.time,
                          ) || null;
                      }

                      if (closedSpotBar && closedFuturesBar) {
                        window.perpImbalance.updateImbalance(
                          state.chart.perpImbalanceComponents,
                          closedSpotBar,
                          closedFuturesBar,
                          oiBar,
                        );
                      }
                    }
                  }
                }
              }
            });
          }
        },
      },
      // After Bybit kline subscription, add Bybit open interest subscription for deltaOIProfile live updates
      {
        m: window.bybitWsManager,
        c: `openInterest.5.${pair.toUpperCase()}USDT`,
        h: (message) => {
          if (message.topic && message.topic.startsWith("openInterest.5.")) {
            // Bybit open interest message structure: { topic: 'openInterest.5.BTCUSDT', data: [{ ... }] }
            const oiArr = Array.isArray(message.data)
              ? message.data
              : [message.data];
            oiArr.forEach((oi) => {
              // Parse open interest bar
              const barTime = Math.floor(Number(oi.timestamp) / 1000);
              const openInterest = parseFloat(oi.openInterest);
              // Find the matching price bar (from alignedBybit)
              const state = chartStates.get(pair);
              if (
                state &&
                state.data &&
                Array.isArray(state.data.alignedBybit)
              ) {
                const priceBar = state.data.alignedBybit.find(
                  (b) => b.time === barTime,
                );
                if (priceBar) {
                  // Add or update openInterestData
                  if (!Array.isArray(state.data.openInterestData))
                    state.data.openInterestData = [];
                  let oiIdx = state.data.openInterestData.findIndex(
                    (d) => d.time === barTime,
                  );
                  const oiPoint = {
                    time: barTime,
                    price: priceBar.close,
                    openInterest: openInterest,
                    priceChange:
                      (priceBar.close - priceBar.open) / priceBar.open,
                    buyFlow: 0,
                    sellFlow: 0,
                    hasOrderFlow: false,
                  };
                  if (oiIdx === -1) {
                    state.data.openInterestData.push(oiPoint);
                  } else {
                    state.data.openInterestData[oiIdx] = oiPoint;
                  }
                  // Keep openInterestData array size reasonable
                  if (state.data.openInterestData.length > CONFIG.maxBars)
                    state.data.openInterestData.shift();
                }
              }
            });
          }
        },
      },
    ];
    channels.forEach(({ m, c, h }) =>
      m?.subscribe(c, (d) => {
        const s = chartStates.get(currentPair);
        if (s && s.isActive && pair === currentPair) h(d);
      }),
    );
    if (state) state.isSubscribed = true;
  }

  /**
   * Unsubscribes from WebSocket channels for a trading pair.
   * @param {string} pair - Trading pair.
   */
  function unsubscribePair(pair) {
    if (!pair) return;
    const lp = pair.toLowerCase();
    ["order_book_", "live_trades_"].forEach((p) =>
      window.bitstampWsManager?.unsubscribe(`${p}${lp}usd`),
    );
    const state = chartStates.get(pair);
    if (state) {
      state.isSubscribed = false;
      state.currentLines = [];
      setTimeout(() => {
        if (
          chartOrderbook?.clearOrderBookLines &&
          state.chart?.priceSeries &&
          !state.chart.priceSeries._internal_isDisposed
        )
          chartOrderbook.clearOrderBookLines(state);
      }, 100);
    }
  }

  const memoryManagement = {
    cleanupInterval: 60000,
    lastCleanup: Date.now(),
    cleanupHistoricalData: (state) => {
      if (!state?.data || Date.now() - memoryManagement.lastCleanup < memoryManagement.cleanupInterval) return;

      memoryManagement.lastCleanup = Date.now();
      const maxBars = CONFIG.maxBars;

      // Efficiently trim arrays in place
      [state.data.priceData, state.data.alignedBybit, state.data.alignedBitstamp]
        .filter(arr => arr?.length > maxBars)
        .forEach(arr => arr.splice(0, arr.length - maxBars));


      if (window.gc) window.gc();
    },
  };

  function _updateSize() {
    const state = chartStates.get(currentPair);
    if (!state || !state.chart) return;
    const container = domCache.get("container");
    if (container) {
      requestAnimationFrame(() => {
        const { clientWidth: w, clientHeight: h } = container;
        if (w > 0 && h > 0) {
          state.chart.priceChart?.resize(w, h);
          window.cvdModule?.resizeCVDChart?.(state.chart.cvdComponents, w, h);
          window.perpCvdModule?.resizeCVDChart?.(
            state.chart.perpCvdComponents,
            w,
            h,
          );
          window.perpImbalance?.resizeIndicator?.(
            state.chart.perpImbalanceComponents,
            w,
            h,
          );
          state.chart.priceChart?.timeScale()?.fitContent?.();
        }
      });
    }
  }
  window.updateSize = utils.throttle(_updateSize, 100);

  let isInitializing = false;

  /**
   * Initializes the chart for the default pair (BTC).
   * @returns {Promise<void>}
   */
  async function initializeChart() {
    if (isInitializing) return;
    isInitializing = true;
    try {
      await waitForModules();
      const container = domCache.get("container");
      if (!container) throw new Error("Container not found");
      const overlay = domCache.get("overlay");
      const data = await preCalculateData("BTC", overlay);
      if (!data) throw new Error("No data for BTC");
      const state = initializeChartAndMeter(container, data, "BTC");
      chartStates.set("BTC", state);
      container.dataset.pair = "BTC";
      subscribePair("BTC");
    } catch (e) {
      handleError("Error initializing chart", e, domCache.get("overlay"));
    } finally {
      isInitializing = false;
    }
  }

  /**
   * Waits for essential modules to load.
   * @param {number} timeout - Timeout in milliseconds.
   * @returns {Promise<void>}
   */
  function waitForModules(timeout = 10000) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const checkModules = () => {
        if (window.cvdModule && window.perpCvdModule && window.perpImbalance) {
          resolve();
        } else if (Date.now() - startTime < timeout) {
          setTimeout(checkModules, 100);
        } else {
          resolve();
        }
      };
      checkModules();
    });
  }

  let switchInProgress = false;
  let lastSwitchAbortController = null;


  /**
   * Switches the chart to a new trading pair.
   * @param {string} newPair - New trading pair.
   * @returns {Promise<void>}
   */
  async function switchPairInternal(newPair) {
    if (newPair === currentPair || switchInProgress) return;
    switchInProgress = true;
    if (lastSwitchAbortController) lastSwitchAbortController.abort();
    const abortController = new AbortController();
    lastSwitchAbortController = abortController;
    const overlay = domCache.get("overlay");
    const previousButtonStates = new Map();
    document
      .querySelectorAll(".pair-button, .popup-timeframe-btn")
      .forEach((btn) => {
        previousButtonStates.set(
          btn.id || btn.dataset.pair || btn.dataset.interval,
          {
            isActive: btn.classList.contains("active"),
            element: btn,
          },
        );
      });
    overlay.style.display = "block";
    const timeout = setTimeout(() => {
      switchInProgress = false;
      if (overlay) {
        overlay.textContent = `Switch to ${newPair} timed out`;
        setTimeout(() => (overlay.style.display = "none"), 3000);
      }
      if (abortController) abortController.abort();
    }, 20000);
    const container = domCache.get("container");

    window.updateActiveButtonState?.(newPair);
    window.clearChartConsole?.();
    window.directConsole?.clear?.();
    async function cleanupPrevious() {
      await Promise.all(
        Array.from(chartStates.entries()).map(async ([pair, state]) => {
          try {
            state.isActive = false;
            unsubscribePair(pair);
            state.throttledFunctions = {};
            await Promise.resolve(state.liquidationManager?.destroy?.());
            await Promise.resolve(state.whaleAlertManager?.destroy?.());
            await Promise.resolve(state.chart?.markerManager?.clearMarkers?.());
            await Promise.resolve(
              window.deltaOiProfileManager?.cleanupAllProfiles?.() ||
                (() => {
                  state.deltaOiProfile?.cleanup?.();
                  state.deltaOiProfile?.destroy?.();
                })(),
            );
            if (
              state.chart?.extras?.markerSeries &&
              !state.chart.extras.markerSeries._internal_isDisposed
            ) {
              state.chart.priceChart?.removeSeries(
                state.chart.extras.markerSeries,
              );
              state.chart.extras.markerSeries = null;
            }
            // Cleanup chart components
            const cleanupTasks = [
              state.chart?.cvdComponents && window.cvdModule?.cleanupCVD?.(
                state.chart.cvdComponents,
                state.chart.cvdComponents?.syncResources,
              ),
              state.chart?.perpCvdComponents && window.perpCvdModule?.cleanupCVD?.(
                state.chart.perpCvdComponents,
                state.chart.perpCvdComponents?.syncResources,
              ),
              state.chart?.perpImbalanceComponents && window.perpImbalance?.cleanupIndicator?.(
                state.chart.perpImbalanceComponents,
              ),
            ].filter(Boolean);

            await Promise.all(cleanupTasks);

            if (state.chart) {
              state.chart.cvdComponents = null;
              state.chart.perpCvdComponents = null;
              state.chart.perpImbalanceComponents = null;
            }
            if (
              state.chart?.priceSeries &&
              !state.chart.priceSeries._internal_isDisposed
            ) {
              Object.values(state.chart.priceLines || {}).forEach((line) => {
                try {
                  line.remove();
                } catch (e) {}
              });
              state.chart.priceChart?.removeSeries(state.chart.priceSeries);
            }
            if (
              state.chart?.priceChart &&
              !state.chart.priceChart._internal_isDisposed
            ) {
              state.chart.priceChart.remove();
              state.chart.priceChart = null;
            }
          } catch (cleanupError) {
            console.error("Error during async chart cleanup:", cleanupError);
          }
        }),
      );
      chartStates.clear();
    }
    async function loadNewChart() {
      const activeButtons = Array.from(
        document.querySelectorAll(".pair-button, .popup-timeframe-btn"),
      ).filter(
        (btn) =>
          btn.id === `${newPair}-button` ||
          btn.dataset.pair === newPair ||
          btn.dataset.interval === (window.currentPopupChartInterval || "60"),
      );
      activeButtons.forEach((btn) => btn.classList.add("active"));
      const chartContainer =
        domCache.get("priceChartElement") ||
        container.querySelector(".price-chart");
      if (chartContainer) {
        const buttonContainer =
          domCache.get("pairSelector") ||
          document.querySelector(".pair-selector");
        const topLevelControls =
          domCache.get("liqControls") ||
          document.querySelector(".liq-controls");
        const chartElements = chartContainer.querySelectorAll(
          ".tv-lightweight-charts, canvas, .pane-separator, .time-scale-box",
        );
        chartElements.forEach((e) => {
          try {
            e.remove();
          } catch (err) {
            console.warn("Error removing chart element:", err);
          }
        });
        const netFlowDiv = document.getElementById("bybit-net-flow-window");
        if (netFlowDiv && netFlowDiv.parentElement)
          netFlowDiv.parentElement.removeChild(netFlowDiv);
        if (buttonContainer && buttonContainer.parentElement) {
          buttonContainer.parentElement.insertBefore(
            buttonContainer,
            buttonContainer.parentElement.firstChild,
          );
        }
        if (
          topLevelControls &&
          container.querySelector(".price-chart-container")
        ) {
          container
            .querySelector(".price-chart-container")
            .appendChild(topLevelControls);
        }
      }
      const priceChartContainer =
        domCache.get("priceChartContainer") ||
        document.querySelector(".price-chart-container");
      if (priceChartContainer) {
        const existingControls = {
          console:
            domCache.get("liquidationConsole") ||
            document.getElementById("liquidation-console"),
          settings:
            domCache.get("liqControls") ||
            document.querySelector(".liq-controls"),
          buttons:
            domCache.get("pairSelector") ||
            document.querySelector(".pair-selector"),
          chartContainer:
            domCache.get("priceChartElement") ||
            document.querySelector(".price-chart"),
        };
        if (!existingControls.console) {
          if (!document.getElementById("liquidation-console")) {
            const newConsole = document.createElement("div");
            newConsole.id = "liquidation-console";
            newConsole.className = "liquidation-console";
            newConsole.style.cssText =
              "display: block; visibility: visible; opacity: 1";
            priceChartContainer.appendChild(newConsole);
          }
          ensureLiquidationConsoleTitle();
        }
        if (existingControls.settings)
          priceChartContainer.appendChild(existingControls.settings);
        if (
          existingControls.buttons &&
          existingControls.buttons.parentElement
        ) {
          existingControls.buttons.parentElement.insertBefore(
            existingControls.buttons,
            existingControls.buttons.parentElement.firstChild,
          );
        }
      }
      await new Promise((r) => setTimeout(r, 200));
      if (newPair === "BTC" && preCalculateDataCache)
        preCalculateDataCache.delete("BTC_" + CONFIG.barInterval);
      if (newPair === "BTC" && container) {
        const priceChartElement = container.querySelector(".price-chart");
        if (priceChartElement) priceChartElement.innerHTML = "";
      }
      const data = await preCalculateData(
        newPair,
        overlay,
        undefined,
        abortController.signal,
      );
      if (abortController.signal.aborted) {
        overlay.textContent = `Switch to ${newPair} cancelled`;
        overlay.style.display = "none";
        return;
      }
      if (!data) {
        overlay.textContent = `Failed to load data for ${newPair}`;
        setTimeout(() => (overlay.style.display = "none"), 3000);
        return;
      }

      const state = initializeChartAndMeter(
        container,
        data,
        newPair,
        undefined,
      );
      if (!state) return;
      state.isActive = true;
      state._versionToken = versionToken;
      chartStates.set(newPair, state);
      if (state.chart.extras?.watermark)
        state.chart.extras.watermark.applyOptions({
          lines: [
            {
              text: `${newPair.toUpperCase()}USD`,
              color: "rgba(255, 255, 255, 0.3)",
              fontSize: 28,
              fontStyle: "bold",
              fontFamily: "Arial",
            },
          ],
          padding: { right: 28 },
        });
      container.dataset.pair = newPair;
      currentPair = newPair;
      window.currentPair = newPair;
      subscribePair(newPair);
      window.clearChartConsole?.();
      const liquidationConsole = document.getElementById("liquidation-console");
      if (liquidationConsole)
        liquidationConsole.style.cssText =
          "display: block; visibility: visible; opacity: 1";
      if (
        window.updateTradingViewWidget &&
        document.querySelector(".tradingview-widget-container").style
          .display !== "none"
      )
        window.updateTradingViewWidget(newPair);
      window.updateSize();
      if (
        window.deltaOiProfileManager?.initialize &&
        (!state.deltaOiProfile || !state.deltaOiProfile.update)
      ) {
        state.deltaOiProfile = window.deltaOiProfileManager.initialize(
          state,
          state.deltaOiProfile?.config || {},
        );
      }

      window.updateSize();
      initSettingsButton();
      ensureLiquidationConsoleTitle();

      if (window.PS && window.PS.resetNetFlow) window.PS.resetNetFlow();
      if (window.PS && window.PS.createNetFlowWindow)
        window.PS.createNetFlowWindow();

      overlay.style.display = "none";
    }
    try {
      await cleanupPrevious();
      await loadNewChart();
    } catch (e) {
      if (abortController.signal.aborted) {
        overlay.textContent = `Switch to ${newPair} cancelled`;
        overlay.style.display = "none";
        overlay.textContent = `Error switching to ${newPair}: ${e.message}`;
        setTimeout(() => (overlay.style.display = "none"), 3000);
      }
    } finally {
      clearTimeout(timeout);
      switchInProgress = false;
    }
  }

  const switchPair = utils.debounce(switchPairInternal, 200);
  window.switchPair = switchPair;

  /**
   * Cleans up resources before page unload.
   */
  function cleanup() {
    window.removeEventListener("memory-pressure", handleMemoryPressure);
    window.removeEventListener("resize", handleResize);
    chartStates.forEach((state) => {
      if (!state) return;
      ["liquidationManager", "whaleAlertManager"].forEach((manager) => {
        if (state[manager]?.destroy) {
          try {
            state[manager].destroy();
          } catch (e) {
            console.debug(`Error destroying ${manager}:`, e);
          }
          state[manager] = null;
        }
      });
      if (state.chart) {
        const { priceChart, extras } = state.chart;
        if (extras?.markerSeries && !extras.markerSeries._internal_isDisposed) {
          try {
            priceChart.removeSeries(extras.markerSeries);
          } catch (e) {
            console.debug("Error removing marker series:", e);
          }
        }
        if (priceChart && !priceChart._internal_isDisposed) {
          try {
            priceChart.remove();
          } catch (e) {
            console.debug("Error removing price chart:", e);
          }
        }
      }
      if (state.config?.ticker?.symbol)
        unsubscribePair(state.config.ticker.symbol);
      // if (state._mainChartUpdateTimeoutId) { // Removed for throttling reversion
      //     clearTimeout(state._mainChartUpdateTimeoutId);
      //     state._mainChartUpdateTimeoutId = null;
      // }
      // state._pendingMainChartBarUpdate = null; // Removed for throttling reversion
      state.isActive = false;
      state.isDisposed = true;
    });
    chartStates.clear();
    window.chartStates = new Map();
  }
  window.addEventListener("beforeunload", cleanup);

  document.addEventListener("DOMContentLoaded", () => {
    const style = document.createElement("style");
    style.textContent = `.liq-controls{position:absolute;bottom:10px;right:10px;display:flex;gap:10px;z-index:100;padding:5px 10px;background-color:rgba(15,20,26,0.8);border-radius:4px;box-shadow:0 2px 5px rgba(0,0,0,0.3)}.liq-apply-btn{background:#444;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:11px}.liq-apply-btn:hover{background:#555}.liq-threshold-input{background:#333;color:#fff;border:1px solid #555;padding:4px;border-radius:3px;width:80px}.settings-dropdown{position:relative}.settings-dropdown-content{display:none;background-color:#1a2026;border:1px solid #555;border-radius:4px;padding:10px;position:absolute;bottom:100%;right:0;min-width:200px;z-index:101}.settings-dropdown-content.show{display:block}.settings-group{margin-bottom:10px}.settings-group-title{font-size:12px;color:#fff;margin-bottom:5px}.settings-group-content{display:flex;gap:5px;align-items:center}.settings-btn{background:#444;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:11px}
        .tv-lightweight-charts .pane-legend-item[data-title*='1 to -1'] {opacity: 0 !important; color: transparent !important;}`;
    document.head.appendChild(style);
    setTimeout(initSettingsButton, 1000);
    setTimeout(ensureLiquidationConsoleTitle, 500);

    // --- ALTS Dropdown Logic ---
    // (Removed: now handled by inline script in index.html)
    // --- END ALTS Dropdown Logic ---

    const observer = new MutationObserver((m) => {
      let shouldEnsure = false;
      for (const mu of m) {
        if (
          mu.type === "childList" &&
          (mu.target.id === "liquidation-console" ||
            mu.target.closest("#liquidation-console"))
        ) {
          shouldEnsure = true;
          break;
        }
        if (
          mu.type === "attributes" &&
          mu.attributeName === "style" &&
          ["liquidation-console", "liquidation-console-title"].includes(
            mu.target.id,
          )
        ) {
          shouldEnsure = true;
          break;
        }
      }
      if (shouldEnsure) ensureLiquidationConsoleTitle();
    });
    const lc = document.getElementById("liquidation-console");
    if (lc)
      observer.observe(lc, {
        childList: true,
        attributes: true,
        attributeFilter: ["style"],
        subtree: true,
      });
    const cc = document.querySelector(".price-chart-container");
    if (cc) observer.observe(cc, { childList: true });
    const debouncedResize = (() => {
      const cbs = new Set();
      let timeoutId;

      const execute = () => cbs.forEach(cb => { try { cb(); } catch {} });
      const trigger = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(execute, 100);
      };

      window.addEventListener("resize", trigger, { passive: true });

      return {
        addCallback: (cb) => {
          if (typeof cb === "function") cbs.add(cb);
          return () => cbs.delete(cb);
        },
        trigger
      };
    })();
    const ps = document.querySelector(".tv-lightweight-charts .pane-separator");
    if (ps) {
      ps.addEventListener("mouseup", debouncedResize.trigger, {
        passive: true,
      });
      new MutationObserver(debouncedResize.trigger).observe(ps, {
        attributes: true,
        attributeFilter: ["style"],
      });
    }
    debouncedResize.addCallback(window.updateSize);
    const container = domCache.get("container");
    if (container) {
      const ro = new ResizeObserver(() => window.updateSize());
      ro.observe(container);
      window.addEventListener("beforeunload", () => ro.disconnect());
    }
  });

  /**
   * Updates price data directly from WebSocket messages.
   * @param {Object} state - Chart state.
   * @param {number} price - Trade price.
   * @param {number} volume - Trade volume.
   * @param {number} type - Trade type (0 for buy, 1 for sell).
   */
  function directUpdatePriceData(state, price, volume, type) {
    const now = Math.floor(Date.now() / 1000);
    const barInterval = state.config.barInterval || 300;
    const barTime = Math.floor(now / barInterval) * barInterval;
    let bar = state.currentBars.currentBarBitstamp;
    if (!bar) {
      bar = state.currentBars.currentBarBitstamp = {
        time: barTime,
        open: price,
        high: price,
        low: price,
        close: price,
        volume,
      };
    } else if (bar.time < barTime) {
      state.data.priceData.push(bar);
      if (state.data.priceData.length > state.config.maxBars)
        state.data.priceData.shift();
      // Inform CVD data store about the updated price data
      if (window.PS && typeof window.PS.setCVDPriceData === "function") {
        window.PS.setCVDPriceData(state.data.priceData);
      }
      bar = state.currentBars.currentBarBitstamp = {
        time: barTime,
        open: price,
        high: price,
        low: price,
        close: price,
        volume,
      };
    } else {
      bar.close = price;
      bar.high = Math.max(bar.high, price);
      bar.low = Math.min(bar.low, price);
      bar.volume += volume;
      // Inform CVD data store about the updated price data (even if it's just a close update to an existing bar)
      if (window.PS && typeof window.PS.setCVDPriceData === "function") {
        // Find and update the last bar in priceData or add if it's a new structure (though this path assumes update)
        const existingBarIndex = state.data.priceData.findIndex(
          (b) => b.time === bar.time,
        );
        if (existingBarIndex !== -1) {
          state.data.priceData[existingBarIndex] = { ...bar }; // Ensure it's a new object for reactivity if store needs it
        } else {
          // This case should ideally be handled by the new bar logic above, but as a fallback:
          state.data.priceData.push({ ...bar });
          if (state.data.priceData.length > state.config.maxBars)
            state.data.priceData.shift();
        }
        window.PS.setCVDPriceData(state.data.priceData);
      }
    }
    state.chart?.priceSeries?.update(bar); // Restored direct update

    state.liquidationManager?.checkForCandleClose?.(Date.now());
    state.whaleAlertManager?.checkForCandleClose?.(Date.now());
    window.cvdModule?.renderPendingCVDUpdates?.(state.chart?.cvdComponents);
    window.perpCvdModule?.renderPendingCVDUpdates?.(
      state.chart?.perpCvdComponents,
    );
    if (!state.metrics)
      state.metrics = {
        buyVolume: 0,
        sellVolume: 0,
        buyValue: 0,
        sellValue: 0,
        liquidations: 0,
        liquidationsMin: 0,
        liquidationsMax: 0,
        spotPressure: 0,
      };
    if (type !== undefined) {
      const isBuy = type === 0;
      const tradeValue = price * volume;
      if (isBuy) {
        state.metrics.buyVolume += volume;
        state.metrics.buyValue += tradeValue;
      } else {
        state.metrics.sellVolume += volume;
        state.metrics.sellValue += tradeValue;
      }
    }
  }

  /**
   * Initializes the settings button and dropdown.
   */
  function initSettingsButton() {
    const existingControls = document.querySelector(".liq-controls");
    if (existingControls) existingControls.remove();
    const container = document.querySelector(".price-chart-container");
    if (!container) return;
    const controlsContainer = document.createElement("div");
    controlsContainer.className = "liq-controls";
    const settingsDropdown = document.createElement("div");
    settingsDropdown.className = "settings-dropdown";
    const settingsButton = document.createElement("button");
    settingsButton.id = "settings-btn";
    settingsButton.className = "settings-btn";
    settingsButton.textContent = "Settings";
    const dropdownContent = document.createElement("div");
    dropdownContent.className = "settings-dropdown-content";
    const savedLiqThreshold = localStorage.getItem("liquidationThreshold");
    const defaultLiqThreshold = savedLiqThreshold
      ? parseFloat(savedLiqThreshold)
      : 100000;
    const savedWhaleThreshold = localStorage.getItem("whaleAlertThreshold");
    const defaultWhaleThreshold = savedWhaleThreshold
      ? parseFloat(savedWhaleThreshold)
      : 100000;
    window.chartStates?.forEach((state) => {
      if (state.liquidationManager)
        state.liquidationManager.dollarThreshold = defaultLiqThreshold;
      if (state.whaleAlertManager)
        state.whaleAlertManager.dollarThreshold = defaultWhaleThreshold;
      ["deltaOiProfile"].forEach((profile) => {
        if (state[profile] && state[profile].config) {
          state[profile].config.normalizationWindow = 1440;
          state[profile].update?.();
        }
      });
    });
    const liqGroup = createSettingsGroup(
      "Liquidation Threshold",
      "liq-threshold-input",
      defaultLiqThreshold,
      "Min $ value",
      applyLiquidationThreshold,
    );
    const whaleGroup = createSettingsGroup(
      "Whale Alert Threshold",
      "whale-threshold-input",
      defaultWhaleThreshold,
      "Min $ value",
      applyWhaleAlertThreshold,
    );
    dropdownContent.appendChild(liqGroup);
    dropdownContent.appendChild(whaleGroup);
    settingsDropdown.appendChild(settingsButton);
    settingsDropdown.appendChild(dropdownContent);
    controlsContainer.appendChild(settingsDropdown);
    container.appendChild(controlsContainer);
    settingsButton.addEventListener("click", () =>
      dropdownContent.classList.toggle("show"),
    );
    document.addEventListener("click", (event) => {
      if (
        !settingsButton.contains(event.target) &&
        !dropdownContent.contains(event.target)
      ) {
        dropdownContent.classList.remove("show");
      }
    });
  }

  /**
   * Creates a settings group for the settings dropdown.
   * @param {string} title - Group title.
   * @param {string} inputId - Input element ID.
   * @param {number} defaultValue - Default input value.
   * @param {string} placeholder - Input placeholder text.
   * @param {Function} applyFunction - Function to apply settings.
   * @param {string} type - Input type.
   * @param {number} min - Minimum input value.
   * @param {number} max - Maximum input value.
   * @param {number} step - Input step value.
   * @returns {HTMLElement} The settings group element.
   */
  function createSettingsGroup(
    title,
    inputId,
    defaultValue,
    placeholder,
    applyFunction,
    type = "number",
    min = 0,
    max = Infinity,
    step = 10000,
  ) {
    const group = document.createElement("div");
    group.className = "settings-group";
    const groupTitle = document.createElement("div");
    groupTitle.className = "settings-group-title";
    groupTitle.textContent = title;
    const content = document.createElement("div");
    content.className = "settings-group-content";
    const input = document.createElement("input");
    input.type = type;
    input.min = min;
    input.max = max;
    input.step = step;
    input.value = defaultValue.toString();
    input.id = inputId;
    input.className = "liq-threshold-input";
    input.placeholder = placeholder;
    const applyButton = document.createElement("button");
    applyButton.textContent = "Apply";
    applyButton.className = "liq-apply-btn";
    content.appendChild(input);
    content.appendChild(applyButton);
    group.appendChild(groupTitle);
    group.appendChild(content);
    applyButton.addEventListener("click", applyFunction);
    input.addEventListener("keyup", (event) => {
      if (event.key === "Enter") applyFunction();
    });
    return group;
  }

  /**
   * Applies threshold settings for liquidation or whale alerts.
   */
  function applyThreshold(type) {
    const inputId = type === 'liquidation' ? 'liq-threshold-input' : 'whale-threshold-input';
    const thresholdInput = document.getElementById(inputId);
    if (!thresholdInput) return;

    const thresholdValue = parseFloat(thresholdInput.value) || 100000;
    const storageKey = `${type}Threshold`;
    const managerKey = `${type}Manager`;

    try {
      localStorage.setItem(storageKey, thresholdValue.toString());

      if (type === 'liquidation') {
        window.currentLiquidationThreshold = thresholdValue;
        window.directConsole?.setThreshold?.(thresholdValue);
      }

      // Set console message threshold
      if (typeof window.setConsoleMessageThreshold === "function") {
        window.setConsoleMessageThreshold(thresholdValue);
      } else {
        window.consoleMessageThreshold = thresholdValue;
      }

      // Update all chart states
      window.chartStates.forEach((state) => {
        if (state[managerKey]) {
          state[managerKey].dollarThreshold = thresholdValue;
        }
      });

      thresholdInput.style.backgroundColor = "rgba(0, 100, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    } catch (e) {
      thresholdInput.style.backgroundColor = "rgba(100, 0, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    }
  }

  const applyLiquidationThreshold = () => applyThreshold('liquidation');
  const applyWhaleAlertThreshold = () => applyThreshold('whaleAlert');

  let lastTitleUpdateTime = 0;
  const MIN_UPDATE_INTERVAL = 500;

  /**
   * Ensures the liquidation console is visible.
   */
  function ensureLiquidationConsoleTitle() {
    const now = Date.now();
    if (now - lastTitleUpdateTime < MIN_UPDATE_INTERVAL) return;
    lastTitleUpdateTime = now;
    const consoleElement = document.getElementById("liquidation-console");
    if (!consoleElement) return;
    const existingTitles = consoleElement.querySelectorAll(
      "#liquidation-console-title",
    );
    existingTitles.forEach((title) => title.remove());
    const style = window.getComputedStyle(consoleElement);
    if (
      style.display === "none" ||
      style.visibility === "hidden" ||
      parseFloat(style.opacity) === 0
    ) {
      consoleElement.style.display = "block";
      consoleElement.style.visibility = "visible";
      consoleElement.style.opacity = "1";
    }
  }

  (async () => {
    await waitForLightweightCharts();
    await initializeChart();
    setTimeout(initSettingsButton, 1000);
  })().catch((e) =>
    handleError("Error in charts.js", e, domCache.get("overlay")),
  );
})();
