(function () {
  // Store the original console methods
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;

  // Configuration
  const MAX_MESSAGES = 50;

  // Get the console element
  function getConsoleElement() {
    return document.getElementById("console-capture");
  }

  // Buffer for messages if DOM not ready
  const _consoleMessageBuffer = [];
  let _consoleDomReady = false;

  function flushConsoleMessageBuffer() {
    if (!_consoleDomReady) return;
    while (_consoleMessageBuffer.length > 0) {
      const { text, type, tradeSize } = _consoleMessageBuffer.shift();
      _addMessageInternal(text, type, tradeSize);
    }
  }

  // Threshold for displaying any message (in USD), dynamic and updatable
  (function () {
    // Restore threshold from localStorage if available
    let _consoleMessageThreshold = 10000;
    const storedThreshold = localStorage.getItem("consoleMessageThreshold");
    if (storedThreshold !== null && !isNaN(parseFloat(storedThreshold))) {
      _consoleMessageThreshold = parseFloat(storedThreshold);
    }
    Object.defineProperty(window, "consoleMessageThreshold", {
      get() {
        return _consoleMessageThreshold;
      },
      set(value) {
        const num = parseFloat(value);
        if (!isNaN(num) && num >= 0) {
          _consoleMessageThreshold = num;
          localStorage.setItem("consoleMessageThreshold", num);
        }
      },
      configurable: true,
    });
    window.setConsoleMessageThreshold = function (value) {
      window.consoleMessageThreshold = value;
    };
    // Register cleanup for global reference
    if (window.CleanupManager && window.CleanupManager.registerCleanup) {
      window.CleanupManager.registerCleanup(() => {
        delete window.consoleMessageThreshold;
        window.setConsoleMessageThreshold = undefined;
      });
    }
  })();

  // Helper to extract the largest number from a string (handles K/M suffixes, e.g., "$2.3K", "$1.5M")
  function extractLargestNumber(str) {
    // Match numbers with optional commas/decimals, optional $ prefix, and optional K/M suffix
    const matches = str.match(/(?:\$)?([\d,.]+)([kKmM]?)/g);
    if (!matches) return null;
    let max = 0;
    for (const match of matches) {
      // Extract number and suffix
      const numMatch = match.match(/(?:\$)?([\d,.]+)([kKmM]?)/);
      if (!numMatch) continue;
      let num = parseFloat(numMatch[1].replace(/,/g, ""));
      let suffix = numMatch[2] ? numMatch[2].toUpperCase() : "";
      if (suffix === "K") num *= 1000;
      if (suffix === "M") num *= 1000000;
      if (!isNaN(num) && num > max) max = num;
    }
    return max > 0 ? max : null;
  }

  // Helper to format trade size with suffixes and up to 3 digits, 1 decimal place
  function formatTradeSize(num) {
    if (typeof num !== "number" || isNaN(num)) return "";
    if (num >= 1_000_000) {
      return (num / 1_000_000).toFixed(1) + "M";
    } else if (num >= 1_000) {
      return (num / 1_000).toFixed(1) + "K";
    } else {
      return num.toFixed(1);
    }
  }

  // Internal function to add a message to the DOM (assumes DOM is ready)
  function _addMessageInternal(text, type, tradeSize) {
    if (typeof tradeSize !== "number" || isNaN(tradeSize) || tradeSize <= 0) {
      // Do not render message if tradeSize is missing or invalid
      if (window.console && window.console.error) {
        window.console.error(
          "[consoleCapture] Refused to render message: missing or invalid tradeSize.",
          text,
          type,
          tradeSize,
        );
      }
      return;
    }
    const threshold = window.consoleMessageThreshold || 0;
    if (tradeSize <= threshold) {
      return;
    }

    const formattedTradeSize = formatTradeSize(tradeSize);
    // Only show the formatted trade size (no message text, no brackets)
    const displayText = `${formattedTradeSize}`;

    const consoleElement = getConsoleElement();
    if (!consoleElement) {
      return;
    }
    const messageElement = document.createElement("div");
    let className = "liquidation-message";
    if (type) className += " " + type;
    messageElement.className = className;
    messageElement.textContent = displayText;
    // Insert at the top, after title if present
    const titleElement = consoleElement.querySelector(".console-capture-title");
    if (titleElement && titleElement.nextSibling) {
      consoleElement.insertBefore(messageElement, titleElement.nextSibling);
    } else if (titleElement) {
      consoleElement.appendChild(messageElement);
    } else {
      consoleElement.insertBefore(messageElement, consoleElement.firstChild);
    }
    // Remove old messages if over limit
    const messages = consoleElement.getElementsByClassName(
      "liquidation-message",
    );
    while (messages.length > MAX_MESSAGES) {
      messages[messages.length - 1].remove();
    }

  }

  // Batched DOM update logic
  const _batchedMessages = [];
  let _batchUpdateScheduled = false;

  function _flushBatchedMessages() {
    if (!_consoleDomReady || !getConsoleElement()) return;
    while (_batchedMessages.length > 0) {
      const { text, type, tradeSize } = _batchedMessages.shift();
      _addMessageInternal(text, type, tradeSize);
    }
  }

  function _scheduleBatchUpdate() {
    if (!_batchUpdateScheduled) {
      _batchUpdateScheduled = true;
      requestAnimationFrame(() => {
        _flushBatchedMessages();
        _batchUpdateScheduled = false;
      });
    }
  }

  // Add a message, buffering if DOM not ready, batching if ready
  function addMessage(text, type, tradeSize) {
    if (!_consoleDomReady || !getConsoleElement()) {
      _consoleMessageBuffer.push({ text, type, tradeSize });
    } else {
      _batchedMessages.push({ text, type, tradeSize });
      _scheduleBatchUpdate();
    }
  }

  // Expose addMessage globally for websocket integration
  // Only accept calls with a valid numeric tradeSize
  window.consoleCaptureAddMessage = addMessage;
  // Register cleanup for global reference
  if (window.CleanupManager && window.CleanupManager.registerCleanup) {
    window.CleanupManager.registerCleanup(() => {
      window.consoleCaptureAddMessage = null;
    });
  }

  // Clear the console
  function clearConsole() {
    const consoleElement = getConsoleElement();
    if (consoleElement) {
      const titleElement = consoleElement.querySelector(
        ".console-capture-title",
      );
      if (titleElement) {
        while (titleElement.nextSibling) {
          consoleElement.removeChild(titleElement.nextSibling);
        }
      } else {
        consoleElement.innerHTML = "";
      }
    }
  }
  window.clearChartConsole = clearConsole;
  // Register cleanup for global reference
  if (window.CleanupManager && window.CleanupManager.registerCleanup) {
    window.CleanupManager.registerCleanup(() => {
      window.clearChartConsole = null;
    });
  }

  // Optionally, override console methods (no-op for log capture)
  console.log = function () {
    originalConsoleLog.apply(console, arguments);
  };
  console.warn = function () {
    originalConsoleWarn.apply(console, arguments);
  };
  console.error = function () {
    originalConsoleError.apply(console, arguments);
  };

  // Initialize console on DOM ready, and flush buffer
  function _onConsoleDomReady() {
    _consoleDomReady = true;
    flushConsoleMessageBuffer();
  }

  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    setTimeout(() => {
      _onConsoleDomReady();
    }, 0);
  } else {
    const domHandler = () =>
      setTimeout(() => {
        _onConsoleDomReady();
      }, 0);
    document.addEventListener("DOMContentLoaded", domHandler);
    // Register cleanup for this event listener
    if (window.CleanupManager && window.CleanupManager.registerCleanup) {
      window.CleanupManager.registerCleanup(() =>
        document.removeEventListener("DOMContentLoaded", domHandler),
      );
    }
  }
})();
