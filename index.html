<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Real-time cryptocurrency dashboard with order books and charts">
    <title>Crypto Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="</title>icon" href="favicon.ico" type="image/x-icon">

    <script>
        // Profile manager stubs
        // window.openInterestProfileManager was removed as it's unused.
    </script>
</head>
<body>
    <!-- Main dashboard container -->
    <div class="main-container">
        <!-- Order books for BTC, ETH, LTC, SOL -->
        <div class="order-books-container">
            <div id="btc-container" class="crypto-container">
                <div id="btc-loading-overlay" class="loading-overlay">Loading...</div>
                <canvas id="btc-orderbook-canvas" class="orderbook-canvas"></canvas>
                <div id="btc-bias-text" class="bias-text">
                    <span class="metric-title" id="btc-ticker-name"></span>
                    <span class="metric-value" id="btc-balance-percent"></span>
                </div>
                <div id="btc-mid-price-text" class="mid-price-text">
                    <span id="btc-mid-price"></span>
                </div>
                <div id="btc-price-scale" class="price-scale">
                    <div class="price-block">
                        <span id="btc-min-price"></span>
                        <span id="btc-lowest-price"></span>
                    </div>
                    <div class="price-block">
                        <span id="btc-max-price"></span>
                        <span id="btc-highest-price"></span>
                    </div>
                </div>
                <div class="meter-wrapper first-meter">
                    <div class="meter-title-value">
                        <span class="metric-title">SPOT Δ:</span>
                        <span class="metric-value hidden-value" id="btc-spot-pressure-value">0.000</span>
                    </div>
                    <canvas id="btc-spot-pressure-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title">PERP Δ:</span>
                        <span class="metric-value hidden-value" id="btc-perp-pressure-value">0.000</span>
                    </div>
                    <canvas id="btc-perp-pressure-cvd-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" style="color: #FFD700;">USD PREM.:</span>
                        <span class="metric-value hidden-value" id="btc-usd-premium-value" style="color: #FFD700;">0.000</span>
                    </div>
                    <canvas id="btc-usd-premium-canvas" class="meter-canvas" style="border-bottom: 2px dashed #FFD700;"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title">OI Δ:</span>
                        <span class="metric-value hidden-value" id="btc-oi-value">0.000</span>
                    </div>
                    <canvas id="btc-oi-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title">LIQ Δ:</span>
                        <span class="metric-value hidden-value" id="btc-liq-value">0.000</span>
                    </div>
                    <canvas id="btc-liq-canvas" class="meter-canvas"></canvas>
                </div>
            </div>
            <div id="eth-container" class="crypto-container">
                <div id="eth-loading-overlay" class="loading-overlay">Loading...</div>
                <canvas id="eth-orderbook-canvas" class="orderbook-canvas"></canvas>
                <div id="eth-bias-text" class="bias-text">
                    <span class="metric-title" id="eth-ticker-name"></span>
                    <span class="metric-value" id="eth-balance-percent"></span>
                </div>
                <div id="eth-mid-price-text" class="mid-price-text">
                    <span id="eth-mid-price"></span>
                </div>
                <div id="eth-price-scale" class="price-scale">
                    <div class="price-block">
                        <span id="eth-min-price"></span>
                        <span id="eth-lowest-price"></span>
                    </div>
                    <div class="price-block">
                        <span id="eth-max-price"></span>
                        <span id="eth-highest-price"></span>
                    </div>
                </div>
                <div class="meter-wrapper first-meter">
                    <div class="meter-title-value">
                        <span class="metric-title" id="eth-spot-pressure-title">SPOT Δ:</span>
                        <span class="metric-value hidden-value" id="eth-spot-pressure-value">0.000</span>
                    </div>
                    <canvas id="eth-spot-pressure-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="eth-perp-cvd-title">PERP Δ:</span>
                        <span class="metric-value hidden-value" id="eth-perp-pressure-value">0.000</span>
                    </div>
                    <canvas id="eth-perp-pressure-cvd-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="eth-oi-title">OI Δ:</span>
                        <span class="metric-value hidden-value" id="eth-oi-value">0.000</span>
                    </div>
                    <canvas id="eth-oi-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="eth-liq-title">LIQ Δ:</span>
                        <span class="metric-value hidden-value" id="eth-liq-value">0.000</span>
                    </div>
                    <canvas id="eth-liq-canvas" class="meter-canvas"></canvas>
                </div>
            </div>
            <div id="ltc-container" class="crypto-container">
                <div id="ltc-loading-overlay" class="loading-overlay">Loading...</div>
                <canvas id="ltc-orderbook-canvas" class="orderbook-canvas"></canvas>
                <div id="ltc-bias-text" class="bias-text">
                    <span class="metric-title" id="ltc-ticker-name"></span>
                    <span class="metric-value" id="ltc-balance-percent"></span>
                </div>
                <div id="ltc-mid-price-text" class="mid-price-text">
                    <span id="ltc-mid-price"></span>
                </div>
                <div id="ltc-price-scale" class="price-scale">
                    <div class="price-block">
                        <span id="ltc-min-price"></span>
                        <span id="ltc-lowest-price"></span>
                    </div>
                    <div class="price-block">
                        <span id="ltc-max-price"></span>
                        <span id="ltc-highest-price"></span>
                    </div>
                </div>
                <div class="meter-wrapper first-meter">
                    <div class="meter-title-value">
                        <span class="metric-title" id="ltc-spot-pressure-title">SPOT Δ:</span>
                        <span class="metric-value hidden-value" id="ltc-spot-pressure-value">0.000</span>
                    </div>
                    <canvas id="ltc-spot-pressure-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="ltc-perp-cvd-title">PERP Δ:</span>
                        <span class="metric-value hidden-value" id="ltc-perp-pressure-value">0.000</span>
                    </div>
                    <canvas id="ltc-perp-pressure-cvd-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="ltc-oi-title">OI Δ:</span>
                        <span class="metric-value hidden-value" id="ltc-oi-value">0.000</span>
                    </div>
                    <canvas id="ltc-oi-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="ltc-liq-title">LIQ Δ:</span>
                        <span class="metric-value hidden-value" id="ltc-liq-value">0.000</span>
                    </div>
                    <canvas id="ltc-liq-canvas" class="meter-canvas"></canvas>
                </div>
            </div>
            <div id="sol-container" class="crypto-container">
                <div id="sol-loading-overlay" class="loading-overlay">Loading...</div>
                <canvas id="sol-orderbook-canvas" class="orderbook-canvas"></canvas>
                <div id="sol-bias-text" class="bias-text">
                    <span class="metric-title" id="sol-ticker-name"></span>
                    <span class="metric-value" id="sol-balance-percent"></span>
                </div>
                <div id="sol-mid-price-text" class="mid-price-text">
                    <span id="sol-mid-price"></span>
                </div>
                <div id="sol-price-scale" class="price-scale">
                    <div class="price-block">
                        <span id="sol-min-price"></span>
                        <span id="sol-lowest-price"></span>
                    </div>
                    <div class="price-block">
                        <span id="sol-max-price"></span>
                        <span id="sol-highest-price"></span>
                    </div>
                </div>
                <div class="meter-wrapper first-meter">
                    <div class="meter-title-value">
                        <span class="metric-title" id="sol-spot-pressure-title">SPOT Δ:</span>
                        <span class="metric-value hidden-value" id="sol-spot-pressure-value">0.000</span>
                    </div>
                    <canvas id="sol-spot-pressure-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="sol-perp-cvd-title">PERP Δ:</span>
                        <span class="metric-value hidden-value" id="sol-perp-pressure-value">0.000</span>
                    </div>
                    <canvas id="sol-perp-pressure-cvd-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="sol-oi-title">OI Δ:</span>
                        <span class="metric-value hidden-value" id="sol-oi-value">0.000</span>
                    </div>
                    <canvas id="sol-oi-canvas" class="meter-canvas"></canvas>
                </div>
                <div class="meter-wrapper">
                    <div class="meter-title-value">
                        <span class="metric-title" id="sol-liq-title">LIQ Δ:</span>
                        <span class="metric-value hidden-value" id="sol-liq-value">0.000</span>
                    </div>
                    <canvas id="sol-liq-canvas" class="meter-canvas"></canvas>
                </div>
            </div>
        </div>
        <!-- Console for liquidations and trades -->
        <div class="console-container">
            <div id="console-capture" class="console-capture">
                <div id="console-capture-title" class="console-capture-title">LIQS/TRADES</div>
                <canvas id="console-title-line" width="85" height="1"></canvas>
            </div>
        </div>
        <!-- Charts and pair selector -->
        <div class="charts-container">
            <div class="chart-container" data-pair="BTC">
                <div class="pair-selector">
                    <button type="button" class="pair-button active" data-pair="BTC">BTC</button>
                    <button type="button" class="pair-button" data-pair="ETH">ETH</button>
                    <button type="button" class="pair-button" data-pair="LTC">LTC</button>
                    <button type="button" class="pair-button" data-pair="SOL">SOL</button>
                    <div class="dropdown">
                        <button type="button" class="pair-button dropdown-toggle" id="more-pairs-btn">ALTS</button>
                        <div class="dropdown-content" id="more-pairs-dropdown"></div>
                    </div>
                    <button type="button" class="pair-button" id="tradingview-toggle-btn">CHART</button>
                    <button type="button" class="pair-button fullscreen-toggle" id="fullscreen-toggle" title="Toggle Fullscreen Mode"></button>
                </div>
                <div class="price-chart-container">
                    <div class="loading-overlay" id="pair-loading-overlay">Loading BTC data...</div>
                    <div class="price-chart"></div>
                    <div id="popup-chart-wrapper" class="popup-chart-wrapper" style="display: none;">
                        <div class="popup-chart-header">
                            <div class="popup-chart-timeframes">
                                <button type="button" class="popup-timeframe-btn" data-interval="1">1m</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="5">5m</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="15">15m</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="30">30m</button>
                                <button type="button" class="popup-timeframe-btn active" data-interval="60">1h</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="240">4h</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="1D">1D</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="1W">1W</button>
                                <button type="button" class="popup-timeframe-btn" data-interval="1M">1M</button>
                            </div>
                            <div class="popup-chart-controls">
                                <button type="button" class="popup-chart-toggle" title="Toggle Chart Visibility">−</button>
                            </div>
                        </div>
                        <div id="popup-chart-container" class="popup-chart-container"></div>
                        <div class="popup-chart-resize-handle" title="Resize"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- XLSX globals for fileLoader.js were here -->




<!-- Core dependencies and modular scripts (all deferred for order and performance) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js" defer></script>
<script src="utils/simpleLoader.js" defer></script>
<script src="utils/commonUtils.js" defer></script>
<script src="utils/mathUtils.js" defer></script>
<script src="profiles/deltaOiProfile.js" defer></script>

<!-- Emergency PERP CVD Data Store Loader -->
<script>
    // Ensure PERP CVD data store is loaded
    window.addEventListener('DOMContentLoaded', function() {
        // Wait a bit for other scripts to load
        setTimeout(function() {
            if (!window.PS || !window.PS.setPerpCVDPriceData) {
                const script = document.createElement('script');
                script.src = 'indicators/data/perpCvdDataStore.js';
                script.onload = function() {
                    // Also check if we have chart data to inject
                    if (window.chartStates) {
                        const btcState = window.chartStates.get('BTC');
                        if (btcState && btcState.data && btcState.data.alignedBybit && btcState.data.alignedBybit.length > 0) {
                            window.PS.setPerpCVDPriceData(btcState.data.alignedBybit.slice());
                        }
                    }
                };
                script.onerror = function(error) {
                    console.error('[Emergency Loader] Failed to load PERP CVD Data Store:', error);
                };
                document.head.appendChild(script);
            }
        }, 3000); // Wait 3 seconds for normal loading
    });
</script>
<script src="utils/consoleFilter.js" defer></script>
<script src="components/popupChartUI.js" defer></script>
<script src="indicators/indicators.js" defer></script>
<script src="utils/bybitOpenInterestPoller.js" defer></script>



<script>
document.addEventListener('DOMContentLoaded', function() {
  const PAIRS = ["ADA", "AAVE", "AVAX", "DOGE", "DOT", "FIL", "LINK", "MATIC", "UNI", "XRP", "XLM", "MKR", "SUSHI", "COMP", "CRV", "1INCH", "LRC", "FET", "DYDX", "INJ", "AXS", "GRT", "SNX", "YFI", "BAND", "KNC", "ENS", "CVX", "RNDR", "AUDIO", "NEXO", "PEPE", "PERP", "PYTH", "RAD", "GODS", "CTSI", "SKL", "FLR"];
  const morePairsBtn = document.getElementById('more-pairs-btn');
  const morePairsDropdown = document.getElementById('more-pairs-dropdown');
  const mainPairs = ["BTC", "ETH", "LTC", "SOL"];
  if (morePairsDropdown) {
    morePairsDropdown.innerHTML = "";
    PAIRS.forEach(pair => {
      if (!mainPairs.includes(pair)) {
        const btn = document.createElement('button');
        btn.type = "button";
        btn.className = "dropdown-item";
        btn.dataset.pair = pair;
        btn.textContent = pair;
        btn.addEventListener('click', function() {
          morePairsDropdown.classList.remove('show');
          // Switch pair logic: update active state and trigger chart logic
          document.querySelectorAll('.pair-button[data-pair], .dropdown-item[data-pair]').forEach(b => {
            b.classList.toggle('active', b.dataset.pair === pair);
          });
          // Update loading overlay message to reflect selected pair
          var loadingOverlay = document.getElementById('pair-loading-overlay');
          if (loadingOverlay) {
            loadingOverlay.textContent = 'Loading ' + pair + ' data...';
          }
          // Reference logic: hide popup chart, switch pair, then show popup chart if it was open
          const popupChartWrapper = document.getElementById('popup-chart-wrapper');
          const wasPopupOpen = popupChartWrapper && popupChartWrapper.style.display === 'flex';
          if (wasPopupOpen) {
            popupChartWrapper.style.display = 'none';
          }
          if (window.switchPair) {
            window.switchPair(pair);
          }
          if (wasPopupOpen) {
            setTimeout(() => {
              popupChartWrapper.style.display = 'flex';
              // Show loading indicator
              const loadingIndicator = document.createElement('div');
              loadingIndicator.id = 'popup-chart-priority-loading';
              loadingIndicator.className = 'loading-indicator';
              loadingIndicator.textContent = `Loading ${pair} chart...`;
              popupChartWrapper.appendChild(loadingIndicator);
              if (window.updatePopupChart) {
                window.updatePopupChart(pair);
              }
              setTimeout(() => {
                const indicator = document.getElementById('popup-chart-priority-loading');
                if (indicator) indicator.remove();
              }, 100);
            }, 300);
          }
        });
        morePairsDropdown.appendChild(btn);
      }
    });
  }
  if (morePairsBtn && morePairsDropdown) {
    morePairsBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      morePairsDropdown.classList.toggle('show');
    });
    document.addEventListener('click', function(e) {
      if (!morePairsBtn.contains(e.target) && !morePairsDropdown.contains(e.target)) {
        morePairsDropdown.classList.remove('show');
      }
    });
  }
});
</script>
</body>
</html>
