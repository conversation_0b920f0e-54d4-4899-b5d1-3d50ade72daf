(function () {
  if (!window.utils) {
    setTimeout(() => {
      if (window.utils) {
      } else {
      }
    }, 500);
    return;
  }
  const { getIndicatorColor } = window.utils;
  const formatLargeNumber = window.commonUtils.formatLargeNumber;
  const { ema, stdev, clamp, lerp, weightedAverage } = window.mathUtils;

  const CVD_CONFIG = window.PS?.CVD_CONFIG ||
    window.CONFIG?.perpCvd || {
      volumeMAPeriod: 90,
      volumeAdjustment: {
        enabled: true,
        buyMultiplier: 1.0,
        sellMultiplier: 1.0,
        useWicks: true,
        useBodySize: true,
        useCloseRelative: true,
      },
      renderOnCandleCloseOnly: true,
      lookbackPeriod: (() => {
        const savedWindow = localStorage.getItem("normalizationWindow");
        return savedWindow ? parseInt(savedWindow) : 1440;
      })(),
      normalize: true,
      smoothing: true,
      sensitivityMultiplier: 1.2,
      normalizationBuffer: 0,
      minSmoothingPeriod: 5,
      maxSmoothingPeriod: 20,
      adaptiveSmoothingFactor: 0.5,
      volumeWeighting: {
        enabled: true,
        weightFactor: 0.5,
      },
    };

  window.PS = window.PS || {};
  window.PS.CVD_CONFIG = CVD_CONFIG;

  const pendingCVDUpdates = {
    lastBarTime: 0,
    lastCvdValue: 0,
    pendingValue: 0,
    pendingEmaValue: 0,
    hasUpdate: false,
    avgVolume: 0,
  };

  async function load6000Bybit5mBars(symbol) {
    if (!window.fetchBybitHistoricalData) {
      throw new Error("Enhanced Bybit historical data fetcher not available");
    }

    const bars = await window.fetchBybitHistoricalData(
      symbol,
      300,
      6000,
      (progressBars) => {},
    );

    if (!bars || bars.length === 0) {
      throw new Error("No bars returned from enhanced Bybit fetcher");
    }

    let invalidBars = 0;
    for (let i = 0; i < bars.length; i++) {
      const bar = bars[i];
      if (
        !bar.time ||
        bar.volume === undefined ||
        isNaN(bar.volume) ||
        isNaN(bar.open) ||
        isNaN(bar.high) ||
        isNaN(bar.low) ||
        isNaN(bar.close)
      ) {
        invalidBars++;
      }
    }

    if (invalidBars > 0) {
      let orderingIssues = 0;
      for (let i = 1; i < bars.length; i++) {
        if (bars[i].time <= bars[i - 1].time) {
          orderingIssues++;
          if (orderingIssues === 1) {
          }
        }
      }
      if (orderingIssues > 0) {
      }
    }
    return bars;
  }

  let historicalCVDData = [];

  let unsubscribePerpCVD = null;
  let lastRenderTime = 0;

  function cleanupCVD(cvdComponents) {
    if (typeof unsubscribePerpCVD === "function") {
      try {
        unsubscribePerpCVD();
      } catch (e) {
      }
      unsubscribePerpCVD = null;
    }

    // Clean up visibility event listeners
    try {
      const handleVisibilityChange = () => {};
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    } catch (e) {
    }

    if (window.PS && window.PS.pendingPerpCVDUpdates) {
      Object.assign(window.PS.pendingPerpCVDUpdates, {
        lastBarTime: 0,
        pendingValue: 0,
        lastCvdValue: 0,
        hasUpdate: false,
        priceData: [],
        normalizedData: [],
        connectionErrors: 0,
        lastSuccessTime: 0,
        maxHistoryLength: 1000,
      });
    }
    if (window.IndicatorChartUtils) {
      window.IndicatorChartUtils.cleanupIndicator(cvdComponents, [], {
        pendingUpdates: window.PS?.pendingPerpCVDUpdates,
      });
    }
  }

  function createCVDChart(container, priceChart) {
    let cvdPane;
    try {
      const panes = priceChart.panes();
      if (panes && panes.length > 1) {
        cvdPane = panes[1];
        cvdPane.applyOptions({ visible: true });
        if (typeof cvdPane.setHeight === "function") {
          cvdPane.setHeight(150);
        }
        cvdPane.applyOptions({
          rightPriceScale: {
            visible: true,
            borderColor: "#2A2A2A",
            scaleMargins: { top: 0.1, bottom: 0.1 },
            formatter: {
              format: (price) => formatLargeNumber(price),
            },
          },
        });
      }
    } catch (e) {
    }

    const cvdSeries = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: {
          type: "volume",
          formatter: (price) => formatLargeNumber(price),
        },
        lineWidth: 1.5,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "PERP CVD",
        pointsVisible: false,
        lastPriceAnimation: 0,
        autoscaleInfoProvider: () => ({
          priceRange: { minValue: -1.05, maxValue: 1.05 },
          margins: { above: 5, below: 5 },
        }),
        crosshairMarkerVisible: false,
      },
      1,
    );

    const cvdMASeries = {
      update: () => {},
      setData: () => {},
      applyOptions: () => {},
      _internal_isDisposed: false,
    };

    const zeroLine = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.perpCvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const levelMinus1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.perpCvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const referenceLines = { level1: level1Line, levelMinus1: levelMinus1Line };

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    try {
      const chartContainer = container.querySelector(".tv-lightweight-charts");
      if (chartContainer) {
        chartContainer.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
      }
    } catch (e) {
    }

    return {
      chart: priceChart,
      pane: cvdPane,
      series: cvdSeries,
      zeroLine: zeroLine,
      referenceLines: referenceLines,
    };
  }

  function calculateAdjustedVolume(bar, prevBar) {
    if (!bar) return 0;
    const volume =
      bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
    if (volume === 0) return 0;
    let isBuyBar = true;
    if (
      window.PS.CVD_CONFIG.volumeAdjustment.useCloseRelative &&
      prevBar &&
      prevBar.close !== undefined &&
      !isNaN(prevBar.close)
    ) {
      isBuyBar = bar.close >= prevBar.close;
    } else {
      isBuyBar = bar.close >= bar.open;
    }
    let adjustmentFactor = 1.0;
    if (window.PS.CVD_CONFIG.volumeAdjustment.useBodySize) {
      const bodySize = Math.abs(bar.close - bar.open);
      const range = bar.high - bar.low;
      if (range > 0 && isFinite(bodySize) && isFinite(range)) {
        const bodySizePercent = bodySize / range;
        adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
      }
    }
    if (window.PS.CVD_CONFIG.volumeAdjustment.useWicks) {
      const totalRange = bar.high - bar.low;
      if (totalRange > 0 && isFinite(totalRange)) {
        const upperWick = bar.high - Math.max(bar.open, bar.close);
        const lowerWick = Math.min(bar.open, bar.close) - bar.low;
        if (isFinite(upperWick) && isFinite(lowerWick)) {
          if (isBuyBar) {
            const lowerWickPercent = lowerWick / totalRange;
            adjustmentFactor *= 1 + lowerWickPercent * 0.8;
          } else {
            const upperWickPercent = upperWick / totalRange;
            adjustmentFactor *= 1 + upperWickPercent * 0.8;
          }
        }
      }
    }
    adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
    return isBuyBar
      ? volume *
          adjustmentFactor *
          window.PS.CVD_CONFIG.volumeAdjustment.buyMultiplier
      : -volume *
          adjustmentFactor *
          window.PS.CVD_CONFIG.volumeAdjustment.sellMultiplier;
  }

  const cvdDataCache = new WeakMap();
  function calculateCVDData(priceData) {
    if (cvdDataCache.has(priceData)) return cvdDataCache.get(priceData);

    if (!priceData || priceData.length === 0) {
      return [];
    }

    const cvdData = [];
    let cumulativeDelta = 0;
    let skippedBars = 0;

    for (let i = 0; i < priceData.length; i++) {
      const bar = priceData[i];
      const prevBar = i > 0 ? priceData[i - 1] : null;

      if (!bar || !bar.time || bar.volume === undefined || isNaN(bar.volume)) {
        skippedBars++;
        continue;
      }

      const barDelta = calculateAdjustedVolume(bar, prevBar);

      if (isNaN(barDelta)) {
        skippedBars++;
        continue;
      }

      cumulativeDelta += barDelta;
      cvdData.push({ time: bar.time, value: cumulativeDelta });
    }

    cvdDataCache.set(priceData, cvdData);
    return cvdData;
  }

  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)";
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)";
    } else {
      return "rgba(170, 170, 170, 0.8)";
    }
  }

  function initializeCVDData(cvdComponents, priceData) {
    if (!priceData || priceData.length === 0) {
      return { cvdData: [], cvdMAData: [] };
    }

    try {
      let closedBars = priceData;
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const lastBarTime = priceData[priceData.length - 1]?.time;
      const isLastBarClosed =
        lastBarTime && lastBarTime <= now - (now % barInterval);
      if (!isLastBarClosed) {
        closedBars = priceData.slice(0, -1);
      }

      const cvdData = calculateCVDData(closedBars);
      if (cvdData.length === 0) {
        return { cvdData: [], cvdMAData: [] };
      }

      const expectedDataPoints = closedBars.filter(
        (bar) =>
          bar && bar.time && bar.volume !== undefined && !isNaN(bar.volume),
      ).length;

      if (cvdData.length < expectedDataPoints * 0.9) {
      }

      const values = cvdData.map((d) => d.value).filter((v) => isFinite(v));

      if (values.length === 0) {
        return { cvdData: [], cvdMAData: [] };
      }

      const lookbackPeriod = CVD_CONFIG.lookbackPeriod || 864;
      const normArr = window.mathUtils.rollingNormalize(values, lookbackPeriod);

      const normalizedCVDData = cvdData.map((point, i) => {
        const normalizedValue = normArr[i];

        // Validate normalized value
        if (!isFinite(normalizedValue)) {
          return { time: point.time, value: 0, color: getNormalizedColor(0) };
        }

        const color = getNormalizedColor(normalizedValue);
        return { time: point.time, value: normalizedValue, color: color };
      });

      if (!window.PS) window.PS = {};
      if (!window.PS.pendingPerpCVDUpdates) {
        window.PS.pendingPerpCVDUpdates = {
          lastBarTime: 0,
          pendingValue: 0,
          hasUpdate: false,
          lastCvdValue: 0,
          normalizedData: [],
          priceData: [],
          avgVolume: 0,
          maxHistoryLength: 1000,
        };
      }
      const globalPendingUpdates = window.PS.pendingPerpCVDUpdates;

      const emptyMAData = cvdData.map((point) => ({
        time: point.time,
        value: 0,
      }));
      const zeroLineData = [];

      if (closedBars.length > 0) {
        const firstTime = closedBars[0].time;
        const lastTime = closedBars[closedBars.length - 1].time;
        zeroLineData.push({ time: firstTime, value: 0 });
        zeroLineData.push({ time: lastTime, value: 0 });

        const lastDataPoint = normalizedCVDData[normalizedCVDData.length - 1];
        if (lastDataPoint) {
          globalPendingUpdates.lastBarTime = lastDataPoint.time;
          globalPendingUpdates.pendingValue = lastDataPoint.value;
          globalPendingUpdates.pendingEmaValue = 0;
          globalPendingUpdates.lastCvdValue = cvdData[cvdData.length - 1].value;
          globalPendingUpdates.hasUpdate = true;
        }
      } else {
        const now = Math.floor(Date.now() / 1000);
        zeroLineData.push({ time: now - 86400, value: 0 });
        zeroLineData.push({ time: now, value: 0 });
      }

      requestAnimationFrame(() => {
        try {
          cvdComponents.series.setData(normalizedCVDData);
          if (normalizedCVDData.length > 0) {
            const lastColor = normalizedCVDData[normalizedCVDData.length - 1].color;
            // Always set both color and titleColor to lastColor for label stability
            cvdComponents.series.applyOptions({
              color: lastColor,
              priceLineColor: lastColor,
              lastValueVisible: false,
              priceLineVisible: false,
              title: 'PERP CVD',
              titleColor: lastColor,
            });
          }
          cvdComponents.zeroLine.setData(zeroLineData);
          if (cvdComponents.referenceLines.level1)
            cvdComponents.referenceLines.level1.setData(
              zeroLineData.map((d) => ({ ...d, value: 1 })),
            );
          if (cvdComponents.referenceLines.levelMinus1)
            cvdComponents.referenceLines.levelMinus1.setData(
              zeroLineData.map((d) => ({ ...d, value: -1 })),
            );
        } catch (e) {
        }
      });

      return { cvdData: normalizedCVDData, cvdMAData: emptyMAData };
    } catch (e) {
      return { cvdData: [], cvdMAData: [] };
    }
  }

  function synchronizeCharts(cvdComponents, priceChart) {
    const syncHandle = window.IndicatorChartUtils
      ? window.IndicatorChartUtils.synchronizeCharts(
          cvdComponents,
          priceChart,
          {
            referenceLevels: {},
          },
        )
      : null;

    const updateIndicatorColor = () => {};

    updateIndicatorColor();

    return {
      unsubscribe: () => {
        try {
          syncHandle?.unsubscribe();
        } catch (e) {}
      },
      updateIndicatorColor: updateIndicatorColor,
    };
  }

  function normalizeCVDWithComponents(value, cvdComponents) {
    try {
      if (historicalCVDData.length === 0) {
        const currentData = cvdComponents.series.data();
        if (currentData && currentData.length > 0) {
          historicalCVDData = currentData.map((d) => ({
            time: d.time,
            value: d.value,
          }));
        }
      }
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;
      if (
        historicalCVDData.length === 0 ||
        historicalCVDData[historicalCVDData.length - 1].time !== currentBarTime
      ) {
        historicalCVDData.push({ time: currentBarTime, value: value });
        if (historicalCVDData.length > CVD_CONFIG.lookbackPeriod * 2) {
          historicalCVDData = historicalCVDData.slice(
            -CVD_CONFIG.lookbackPeriod * 2,
          );
        }
      } else {
        historicalCVDData[historicalCVDData.length - 1].value = value;
      }
      const lookbackData = historicalCVDData.slice(-CVD_CONFIG.lookbackPeriod);
      if (lookbackData.length === 0) {
        return value >= 0 ? 0.5 : -0.5;
      }
      const min = Math.min(...lookbackData.map((d) => d.value));
      const max = Math.max(...lookbackData.map((d) => d.value));
      return normalizeCVD(value, min, max);
    } catch (e) {
      return value >= 0 ? 0.5 : -0.5;
    }
  }

  function normalizeCVD(value, min, max) {
    if (max === min) return value >= 0 ? 0.5 : -0.5;
    return ((value - min) / (max - min)) * 2 - 1;
  }

  function updateCVD(cvdComponents, bar, prevBar, lastCvdValue = 0) {
    if (!bar || bar.volume === undefined || isNaN(bar.volume)) {
      return lastCvdValue;
    }
    if (!cvdComponents?.series || cvdComponents.series._internal_isDisposed) {
      return lastCvdValue;
    }

    try {
      let timeGapMinutes = 0;
      if (prevBar && bar.time > prevBar.time) {
        timeGapMinutes = (bar.time - prevBar.time) / 60;
      }

      const volume =
        bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
      const barDelta = calculateAdjustedVolume(bar, prevBar);
      let weightedDelta = barDelta;

      if (
        window.PS.CVD_CONFIG.volumeWeighting &&
        window.PS.CVD_CONFIG.volumeWeighting.enabled &&
        volume > 0
      ) {
        let avgVolume = volume;
        if (pendingCVDUpdates && pendingCVDUpdates.avgVolume) {
          avgVolume = pendingCVDUpdates.avgVolume * 0.9 + volume * 0.1;
        }
        if (pendingCVDUpdates) {
          pendingCVDUpdates.avgVolume = avgVolume;
        }
        const volumeRatio = volume / avgVolume;
        const weightFactor = window.PS.CVD_CONFIG.volumeWeighting.weightFactor;
        weightedDelta =
          (barDelta * (1 + weightFactor * (volumeRatio - 1))) /
          (1 + weightFactor);
      }

      const newCvdValue = lastCvdValue + weightedDelta;
      let smoothedCvdValue = newCvdValue;

      if (timeGapMinutes > 5) {
        const prevCvdValue = pendingCVDUpdates.lastCvdValue || lastCvdValue;
        const basePeriod = 5;
        const additionalSmoothing =
          timeGapMinutes * window.PS.CVD_CONFIG.adaptiveSmoothingFactor;
        const adaptivePeriod = Math.min(
          window.PS.CVD_CONFIG.maxSmoothingPeriod,
          Math.max(
            window.PS.CVD_CONFIG.minSmoothingPeriod,
            basePeriod + additionalSmoothing,
          ),
        );
        const alpha = 2 / (adaptivePeriod + 1);
        smoothedCvdValue = alpha * newCvdValue + (1 - alpha) * prevCvdValue;
      }

      const normalizedCVDValue = normalizeCVDWithComponents(
        smoothedCvdValue,
        cvdComponents,
      );
      let displayValue = normalizedCVDValue;
      if (isNaN(displayValue)) {
        displayValue = 0;
      }

      // Update pendingCVDUpdates state, but do not directly update the chart here.
      // Chart updates will be handled by setupCVDUpdateInterval based on these pending values.
      pendingCVDUpdates.pendingValue = displayValue;
      pendingCVDUpdates.lastCvdValue = newCvdValue; // Store the raw cumulative value
      pendingCVDUpdates.hasUpdate = true; // Indicate that there's a new pending value

      // The actual chart update (series.update) should be triggered by the subscription callback
      // in setupCVDUpdateInterval when a new bar closes.

      if (
        cvdComponents.syncResources &&
        typeof cvdComponents.syncResources.updateIndicatorColor === "function"
      ) {
        // This can still be called if it's for non-series related UI updates like legend color
        cvdComponents.syncResources.updateIndicatorColor();
      }

      return newCvdValue; // Return the raw cumulative value
    } catch (e) {
      // console.error("Error in updateCVD:", e); // Optional: for debugging
      return lastCvdValue;
    }
  }

  function resizeCVDChart(cvdComponents, _width, height) {
    try {
      if (cvdComponents && cvdComponents.chart) {
        if (cvdComponents.pane) {
          const cvdHeight = Math.max(150, Math.floor(height * 0.2));
          if (typeof cvdComponents.pane.setHeight === "function") {
            cvdComponents.pane.setHeight(cvdHeight);
          }
        } else {
          const chartContainer = document.querySelector(
            ".price-chart-container",
          );
          if (chartContainer) {
            const chartElement = chartContainer.querySelector(
              ".tv-lightweight-charts",
            );
            if (chartElement) {
              chartElement.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
              try {
                const panes = cvdComponents.chart.panes();
                if (panes && panes.length > 1) {
                  const cvdPane = panes[1];
                  cvdComponents.pane = cvdPane;
                  const cvdHeight = Math.max(150, Math.floor(height * 0.2));
                  if (typeof cvdPane.setHeight === "function") {
                    cvdPane.setHeight(cvdHeight);
                  }
                }
              } catch (paneError) {
                const paneElements = chartElement.querySelectorAll(
                  ".tv-lightweight-charts__pane",
                );
                if (paneElements && paneElements.length > 1) {
                  const cvdPaneElement = paneElements[1];
                  if (cvdPaneElement) {
                    cvdPaneElement.style.zIndex = "3";
                    cvdPaneElement.style.borderTop = "1px solid #2A2A2A";
                    cvdPaneElement.style.boxSizing = "border-box";
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {
    }
  }

  function renderPendingCVDUpdates(cvdComponents) {
    if (!cvdComponents?.series || cvdComponents.series._internal_isDisposed)
      return;

    // Throttle to 2 seconds (reduced from 1s)
    const now = Date.now();
    if (now - lastRenderTime < 2000) return;
    lastRenderTime = now;

    // Defer non-critical work - This function may no longer be needed if setupCVDUpdateInterval handles all rendering.
    // If kept, it should only be for exceptional cases or UI elements not tied to series data.
    // For now, we'll comment out its content to prevent interference.
    /*
    requestIdleCallback(
      () => {
        try {
          // Check if we have valid pending data
          if (!window.PS?.pendingPerpCVDUpdates) {
            return;
          }

          const pendingUpdates = window.PS.pendingPerpCVDUpdates;
          const currentBarTime = Math.floor(Date.now() / 1000 / 300) * 300;
          const isNewCandle = currentBarTime > pendingUpdates.lastBarTime;

          // This logic should ideally be handled by the subscription callback
          if (pendingUpdates.hasUpdate || isNewCandle) {
            const value = pendingUpdates.pendingValue;

            if (value === undefined || isNaN(value)) {
              return;
            }

            const cvdColor = getNormalizedColor(value);

            // series.update should be called from setupCVDUpdateInterval
            // cvdComponents.series.update({
            //   time: pendingUpdates.lastBarTime,
            //   value,
            //   color: cvdColor,
            // });
            // cvdComponents.series.applyOptions({
            //   priceLineColor: cvdColor,
            //   lastValueVisible: false,
            //   priceLineVisible: false,
            //   title: 'PERP CVD',
            //   titleColor: cvdColor,
            // });

            if (isNewCandle) {
              // This state update should also be centralized if possible
              // pendingUpdates.lastBarTime = currentBarTime;
              // if (pendingUpdates.lastCvdValue !== undefined) {
              //   pendingUpdates.pendingValue = normalizeCVDWithComponents(
              //     pendingUpdates.lastCvdValue,
              //     cvdComponents,
              //   );
              // }
            }
            // pendingUpdates.hasUpdate = false; // This flag is managed by the subscription now
          }
        } catch (e) {
          if (window.PS?.pendingPerpCVDUpdates) {
            window.PS.pendingPerpCVDUpdates.connectionErrors =
              (window.PS.pendingPerpCVDUpdates.connectionErrors || 0) + 1;
          }
        }
      },
      { timeout: 1000 },
    );
    */
  }

  function setupCVDUpdateInterval(cvdComponents) {
    // If a previous subscription's unsubscribe function exists, call it.
    if (typeof unsubscribePerpCVD === "function") {
      console.log("PERP CVD: Cleaning up existing subscription before attempting a new one.");
      unsubscribePerpCVD(); // This should be the actual unsubscribe from PS.subscribePerpCVD
      unsubscribePerpCVD = null;
    }

    // Module-level variable to hold the cleanup function for resources created by a successful setup
    let currentCleanupFunction = null;

    const MAX_SUB_ATTEMPTS = 5; // Max attempts to find window.PS.subscribePerpCVD
    const SUB_RETRY_DELAY = 500; // Delay between attempts
    let subAttemptCount = 0;

    function trySubscribe() {
      subAttemptCount++;

      if (typeof window.PS?.subscribePerpCVD === "function") {

        // Initialize global pending updates state if not already present
        if (!window.PS.pendingPerpCVDUpdates) {
          window.PS.pendingPerpCVDUpdates = {
            lastBarTime: 0,
            pendingValue: 0,
            lastCvdValue: 0,
            hasUpdate: false,
            connectionErrors: 0,
            lastSuccessTime: Date.now(),
          };
        }
        const pendingUpdates = window.PS.pendingPerpCVDUpdates;

        const handleVisibilityChange = () => {
          if (document.visibilityState === "visible" && pendingUpdates.hasUpdate) {
            try {
              if (isFinite(pendingUpdates.pendingValue) && pendingUpdates.lastBarTime > 0) {
                const color = getNormalizedColor(pendingUpdates.pendingValue);
                cvdComponents.series.update({
                  time: pendingUpdates.lastBarTime,
                  value: pendingUpdates.pendingValue,
                  color,
                });
                cvdComponents.series.applyOptions({
                  priceLineColor: color, lastValueVisible: false, priceLineVisible: false, title: 'PERP CVD', titleColor: color,
                });
              }
            } catch (e) {
              console.warn("PERP CVD: Error updating chart on visibility change:", e);
            }
          }
        };
        document.addEventListener("visibilitychange", handleVisibilityChange);

        let reconnectAttempts = 0;
        const maxInternalReconnectAttempts = 3; // Renamed to avoid confusion
        const internalReconnectDelay = 5000;   // Renamed
        let healthCheckInterval = null; // Declare here so it can be accessed in attemptInternalReconnection

        // This will hold the unsubscribe function from the actual successful subscription
        let actualUnsubscribeFromStore = null;

        const createInternalSubscription = () => {
          try {
            actualUnsubscribeFromStore = window.PS.subscribePerpCVD((cvdData) => { // Assign to actualUnsubscribeFromStore
              if (!cvdComponents || cvdComponents.series._internal_isDisposed) {
                return;
              }
              try {
                // Always update success time when callback is called, regardless of data validity
                pendingUpdates.connectionErrors = 0;
                pendingUpdates.lastSuccessTime = Date.now();
                reconnectAttempts = 0;

                if (cvdData && typeof cvdData.time === 'number' && typeof cvdData.value !== 'undefined') {
                  if (!isFinite(cvdData.value)) {
                    console.warn("PERP CVD: Received non-finite CVD value:", cvdData.value);
                    return;
                  }

                  if (cvdData.time > pendingUpdates.lastBarTime) {
                    if (pendingUpdates.hasUpdate && isFinite(pendingUpdates.pendingValue) && pendingUpdates.lastBarTime > 0) {
                      const color = getNormalizedColor(pendingUpdates.pendingValue);
                      cvdComponents.series.update({ time: pendingUpdates.lastBarTime, value: pendingUpdates.pendingValue, color: color });
                      cvdComponents.series.applyOptions({ priceLineColor: color, lastValueVisible: false, priceLineVisible: false, title: 'PERP CVD', titleColor: color });
                    }
                    pendingUpdates.lastBarTime = cvdData.time;
                    pendingUpdates.pendingValue = cvdData.value;
                    pendingUpdates.hasUpdate = true;
                  } else if (cvdData.time === pendingUpdates.lastBarTime) {
                    pendingUpdates.pendingValue = cvdData.value;
                    pendingUpdates.hasUpdate = true;
                  }
                  if (pendingUpdates.normalizedData && pendingUpdates.normalizedData.length > pendingUpdates.maxHistoryLength) {
                    pendingUpdates.normalizedData = pendingUpdates.normalizedData.slice(-pendingUpdates.maxHistoryLength);
                  }
                  if (pendingUpdates.priceData && pendingUpdates.priceData.length > pendingUpdates.maxHistoryLength) {
                    pendingUpdates.priceData = pendingUpdates.priceData.slice(-pendingUpdates.maxHistoryLength);
                  }
                }
              } catch (e) {
                console.error("PERP CVD: Error processing subscribed CVD data:", e);
                pendingUpdates.connectionErrors = (pendingUpdates.connectionErrors || 0) + 1;
                if (pendingUpdates.connectionErrors > 5) attemptInternalReconnection();
              }
            });
             // Store the real unsubscribe function at the module level
            unsubscribePerpCVD = actualUnsubscribeFromStore;

          } catch (e) {
            console.error("PERP CVD: Failed to create internal subscription:", e);
            attemptInternalReconnection();
          }
        };

        const attemptInternalReconnection = () => {
          if (reconnectAttempts >= maxInternalReconnectAttempts) {
            // Clear the health check interval to stop further reconnection attempts
            if (healthCheckInterval) {
              clearInterval(healthCheckInterval);
            }
            return;
          }
          reconnectAttempts++;
          if (actualUnsubscribeFromStore) { // Use the specific unsubscribe for the store subscription
            try { actualUnsubscribeFromStore(); } catch (e) { /* ignore */ }
            actualUnsubscribeFromStore = null;
            unsubscribePerpCVD = null; // Also clear the module-level one
          }
          setTimeout(() => {
            if (cvdComponents && !cvdComponents.series._internal_isDisposed) createInternalSubscription();
          }, internalReconnectDelay * reconnectAttempts);
        };

        healthCheckInterval = setInterval(() => {
          const timeSinceLastSuccess = Date.now() - (pendingUpdates.lastSuccessTime || 0);
          if (timeSinceLastSuccess > 30000) {
            attemptInternalReconnection();
          }
        }, 10000);

        createInternalSubscription(); // Initial call to subscribe

        // This cleanup function is for resources created *within this successful setup*
        currentCleanupFunction = () => {
          if (healthCheckInterval) clearInterval(healthCheckInterval);
          document.removeEventListener("visibilitychange", handleVisibilityChange);
          if (actualUnsubscribeFromStore) { // Ensure we call the correct unsubscribe
            try { actualUnsubscribeFromStore(); } catch(e) {/*ignore*/}
            actualUnsubscribeFromStore = null;
          }
           unsubscribePerpCVD = null; // Clear module-level one too
        };
        // The main module-scoped `unsubscribePerpCVD` is now assigned within `createInternalSubscription`
        // and will be the one returned by `window.PS.subscribePerpCVD`

      } else if (subAttemptCount < MAX_SUB_ATTEMPTS) {
        setTimeout(trySubscribe, SUB_RETRY_DELAY);
      } else {
        console.error(`PERP CVD: Failed to find window.PS.subscribePerpCVD after ${MAX_SUB_ATTEMPTS} attempts. Indicator will not receive live updates.`);
      }
    }

    trySubscribe(); // Start the subscription attempt process

    // The `setupCVDUpdateInterval` function itself doesn't need to return a cleanup for the subscription,
    // as `unsubscribePerpCVD` (module scope) handles that. It returns cleanup for its own retry logic if needed,
    // but the retry is self-contained. The `cleanupCVD` function will call the module-scoped `unsubscribePerpCVD`.
    // However, if `trySubscribe` successfully sets up intervals/listeners, IT should return a way to clean THEM up.
    // This is tricky. `currentCleanupFunction` will be set if `trySubscribe` succeeds.
    // The `cleanupCVD` function at the module level is responsible for calling the module-scoped `unsubscribePerpCVD`.
    // Let's simplify: `setupCVDUpdateInterval` doesn't need to return a complex cleanup.
    // `cleanupCVD` will handle calling the module-scoped `unsubscribePerpCVD`.
    // The `healthCheckInterval` and `visibilitychange` listener cleanup needs to be tied to `unsubscribePerpCVD`.
    // This is getting complex. The module-scoped `unsubscribePerpCVD` should be the single function to call for full cleanup.
    // So, when `actualUnsubscribeFromStore` is called, it should also clear the interval and remove listener.
    // Let's make `unsubscribePerpCVD` (module scope) the comprehensive cleanup.

    // Redefine module-scoped unsubscribePerpCVD to be comprehensive when a subscription is active
    // This is done inside the successful subscription path.
    // If trySubscribe fails completely, unsubscribePerpCVD remains null or its previous value (if any from a prior call).
  }

  function updateCustomCvdLegend(color) {
    let legend = document.getElementById('perp-cvd-legend');
    if (!legend) {
      legend = document.createElement('div');
      legend.id = 'perp-cvd-legend';
      legend.style.position = 'absolute';
      legend.style.top = '8px';
      legend.style.right = '16px';
      legend.style.zIndex = '10';
      legend.style.fontWeight = 'bold';
      legend.style.fontSize = '13px';
      legend.style.pointerEvents = 'none';
      legend.style.userSelect = 'none';
      legend.style.padding = '2px 8px';
      legend.style.borderRadius = '4px';
      legend.style.background = 'rgba(15,20,26,0.7)';
      legend.textContent = 'PERP CVD';
      document.body.appendChild(legend);
    }
    legend.style.color = color;
  }

  try {
    window.perpCvdModule = window.perpCvdModule || {
      createCVDChart,
      calculateAdjustedVolume,
      calculateCVDData,
      getNormalizedColor,
      initializeCVDData,
      synchronizeCharts,
      normalizeCVDWithComponents,
      updateCVD,
      resizeCVDChart,
      cleanupCVD,
      renderPendingCVDUpdates,
      setupCVDUpdateInterval,
      unsubscribePerpCVD: () => {
        if (unsubscribePerpCVD) unsubscribePerpCVD();
      },
    };
  } catch (e) {
    window.perpCvdModule = {
      createCVDChart: () => ({ series: null, syncResources: null }),
      calculateAdjustedVolume: () => {},
      calculateCVDData: () => [],
      getNormalizedColor: () => "#aaa",
      initializeCVDData: () => {},
      synchronizeCharts: () => ({}),
      normalizeCVDWithComponents: () => 0,
      updateCVD: () => {},
      resizeCVDChart: () => {},
      cleanupCVD: () => {},
      renderPendingCVDUpdates: () => {},
      setupCVDUpdateInterval: () => {},
      unsubscribePerpCVD: () => {},
    };
  }

  document.addEventListener("DOMContentLoaded", async () => {
    if (!window.utils || !window.mathUtils) {
      return;
    }

    const configObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-config"
        ) {
          const newConfig = JSON.parse(
            mutation.target.getAttribute("data-config"),
          );
          if (newConfig.perpCvd) {
            Object.assign(CVD_CONFIG, newConfig.perpCvd);
          }
        }
      }
    });

    configObserver.observe(document.body, {
      attributes: true,
      childList: false,
      subtree: false,
    });

    let priceChartContainer;
    const waitForPriceChart = new Promise((resolve) => {
      const checkExist = setInterval(() => {
        priceChartContainer = document.querySelector(".price-chart-container");
        if (priceChartContainer) {
          clearInterval(checkExist);
          resolve(priceChartContainer);
        }
      }, 500);
    });

    try {
      await waitForPriceChart;

      // Ensure LightweightCharts is available
      if (!window.LightweightCharts) {
        console.error("PERP CVD: LightweightCharts library is not loaded.");
        return;
      }

      // Only create chart if not already present
      let priceChart = window.priceChart;
      if (!priceChart) {
        priceChart = window.LightweightCharts.createChart(
          priceChartContainer,
          {
            layout: {
              backgroundColor: "rgba(15, 20, 26, 1.0)",
              textColor: "#FFFFFF",
            },
            grid: {
              vertLines: {
                color: "#2A2A2A",
              },
              horzLines: {
                color: "#2A2A2A",
              },
            },
            crosshair: {
              mode: window.LightweightCharts.CrosshairMode.Normal,
            },
            timeScale: {
              borderColor: "#2A2A2A",
            },
          },
        );
        window.priceChart = priceChart;
      }

      const cvdComponents = createCVDChart(priceChartContainer, priceChart);

      const symbol = "BTCUSDTPERP";
      const bars = await load6000Bybit5mBars(symbol);

      initializeCVDData(cvdComponents, bars);
      setupCVDUpdateInterval(cvdComponents);

      // Only observe resize if container and chart exist
      if (priceChartContainer && priceChart) {
        const resizeObserver = new ResizeObserver(() => {
          const { width, height } = priceChartContainer.getBoundingClientRect();
          resizeCVDChart(cvdComponents, width, height);
        });
        resizeObserver.observe(priceChartContainer);
      }
    } catch (e) {
      console.error("PERP CVD: Initialization error:", e);
    }
  });
})();
