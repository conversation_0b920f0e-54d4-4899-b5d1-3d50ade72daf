// peckersocket 3.0/utils/statePersistence.js

(function() {
    'use strict';

    window.saveUIStateToCache = function() {
        try {
            try {
                localStorage.setItem('scrollY', window.scrollY);
            } catch (e) {
                console.warn('Failed to save scrollY state:', e.message);
            }

            if (window.chartStates && typeof window.chartStates.forEach === 'function') {
                const chartStatesObj = {};
                window.chartStates.forEach((state, key) => {
                    try {
                        const chartStateToSave = {
                            lastPrice: state.lastPrice,
                            lastFullDrawTime: state.lastFullDrawTime,
                            persistentBlocks: state.persistentBlocks,
                        };

                        if (state.chart && state.chart.priceChart && typeof state.chart.priceChart.timeScale === 'function') {
                            const timeScale = state.chart.priceChart.timeScale();
                            if (typeof timeScale.getVisibleLogicalRange === 'function') {
                                chartStateToSave.visibleLogicalRange = timeScale.getVisibleLogicalRange();
                            }
                        }
                        // Potentially add other chart-specific settings here if easily serializable

                        chartStatesObj[key] = chartStateToSave;
                    } catch (e) {
                        console.warn(`Failed to save state for chart ${key}:`, e.message);
                    }
                });
                try {
                    localStorage.setItem('chartStates', JSON.stringify(chartStatesObj));
                } catch (e) {
                    console.warn('Failed to save chartStates object to localStorage:', e.message);
                }
            }

            if (window.chartOrderbook && window.chartOrderbook._lastOrderBookSnapshot) {
                try {
                    localStorage.setItem('orderBookData', JSON.stringify(window.chartOrderbook._lastOrderBookSnapshot));
                } catch (e) {
                    console.warn('Failed to save orderBookData state:', e.message);
                }
            }

            // Save popup chart state if available
            if (typeof window.savePopupChartState === 'function') {
                try {
                    window.savePopupChartState();
                } catch (e) {
                    console.warn('Failed to save popupChartState:', e.message);
                }
            }
        } catch (e) {
            console.warn('Overall error in saveUIStateToCache:', e.message);
        }
    };

    window.restoreUIStateFromCache = function() {
        try {
            // Restore scroll position
            const scrollYStr = localStorage.getItem('scrollY');
            if (scrollYStr !== null) {
                const scrollY = parseInt(scrollYStr, 10);
                if (Number.isFinite(scrollY) && scrollY >= 0) {
                    window.scrollTo(0, scrollY);
                } else {
                    console.warn('Invalid scrollY value found in localStorage:', scrollYStr);
                }
            }

            // Restore chartStates (if available)
            const chartStatesStr = localStorage.getItem('chartStates');
            if (chartStatesStr) {
                try {
                    const chartStatesObj = JSON.parse(chartStatesStr);
                    if (window.chartStates && typeof window.chartStates.set === 'function') {
                        Object.entries(chartStatesObj).forEach(([key, savedState]) => {
                            try {
                                if (!window.chartStates.has(key)) {
                                    window.chartStates.set(key, {});
                                }
                                const liveChartState = window.chartStates.get(key);
                                Object.assign(liveChartState, {
                                    lastPrice: savedState.lastPrice,
                                    lastFullDrawTime: savedState.lastFullDrawTime,
                                    persistentBlocks: savedState.persistentBlocks,
                                    // Store the range for charts.js to potentially use later
                                    savedVisibleLogicalRange: savedState.visibleLogicalRange
                                });

                                // The savedVisibleLogicalRange is stored on liveChartState.
                                // charts.js will be responsible for applying it when the chart is initialized.
                            } catch (e) {
                                console.warn(`Failed to restore state for chart ${key}:`, e.message);
                            }
                        });
                    }
                } catch (e) {
                    console.warn('Failed to parse chartStates from localStorage:', e.message);
                }
            }

            // Restore orderbook data (if available)
            const orderBookDataStr = localStorage.getItem('orderBookData');
            if (orderBookDataStr && window.chartOrderbook) {
                try {
                    window.chartOrderbook._lastOrderBookSnapshot = JSON.parse(orderBookDataStr);
                } catch (e) {
                    console.warn('Failed to parse orderBookData from localStorage:', e.message);
                }
            }

            // Restore popup chart state if available
            if (typeof window.loadPopupChartState === 'function') {
                try {
                    window.loadPopupChartState();
                } catch (e) {
                    console.warn('Failed to restore popupChartState:', e.message);
                }
            }
        } catch (e) {
            console.warn('Overall error in restoreUIStateFromCache:', e.message);
        }
    };

})();
