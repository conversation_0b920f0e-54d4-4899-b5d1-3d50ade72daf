/**
 * Fallback Initialization System
 * Provides robust initialization when the main loading system fails
 */

(function() {
    'use strict';

    // Configuration for fallback loading
    const FALLBACK_CONFIG = {
        timeout: 5000,
        retryAttempts: 3,
        retryDelay: 1000,
        essentialScripts: [
            'utils/mathUtils.js',
            'utils/config.js',
            'wsmanager.js',
            'modules/orderbook.js',
            'modules/charts.js'
        ],
        criticalDependencies: [
            'window.LightweightCharts',
            'window.WebSocketManager',
            'window.utils'
        ]
    };

    class FallbackInitializer {
        constructor() {
            this.loadedScripts = new Set();
            this.failedScripts = new Set();
            this.retryCount = new Map();
            this.isInitialized = false;
            this.startTime = Date.now();
        }

        async initialize() {
            if (this.isInitialized) return;
            
            console.log('Fallback initialization started');
            
            try {
                // Check if main loading system is working
                await this.waitForMainLoader();
                
                // If main loader exists, let it handle initialization
                if (window.LoadingManager && window.loadingManager) {
                    console.log('Main loading system found, deferring to it');
                    return;
                }
                
                // Otherwise, proceed with fallback
                await this.performFallbackInit();
                
            } catch (error) {
                console.error('Fallback initialization failed:', error);
                await this.performMinimalInit();
            }
        }

        async waitForMainLoader() {
            return new Promise((resolve) => {
                let attempts = 0;
                const maxAttempts = 10;
                
                const check = () => {
                    if (window.LoadingManager) {
                        resolve(true);
                    } else if (attempts++ < maxAttempts) {
                        setTimeout(check, 100);
                    } else {
                        resolve(false);
                    }
                };
                
                check();
            });
        }

        async performFallbackInit() {
            console.log('Performing fallback initialization');
            
            // Load essential scripts first
            await this.loadEssentialScripts();
            
            // Wait for critical dependencies
            await this.waitForDependencies();
            
            // Initialize basic functionality
            await this.initializeBasics();
            
            // Load remaining modules
            await this.loadRemainingModules();
            
            this.isInitialized = true;
            this.notifyReady();
        }

        async performMinimalInit() {
            console.warn('Performing minimal initialization');
            
            // Create basic UI elements
            this.createBasicUI();
            
            // Initialize minimal WebSocket connection
            await this.initializeMinimalWebSocket();
            
            // Show limited functionality message
            this.showLimitedMessage();
            
            this.notifyReady(false);
        }

        async loadEssentialScripts() {
            const promises = FALLBACK_CONFIG.essentialScripts.map(script => 
                this.loadScriptWithRetry(script)
            );
            
            const results = await Promise.allSettled(promises);
            
            results.forEach((result, index) => {
                if (result.status === 'rejected') {
                    console.warn(`Failed to load essential script: ${FALLBACK_CONFIG.essentialScripts[index]}`);
                }
            });
        }

        async loadScriptWithRetry(src) {
            const retryKey = src;
            let attempts = this.retryCount.get(retryKey) || 0;

            if (this.loadedScripts.has(src)) {
                return Promise.resolve();
            }

            if (this.failedScripts.has(src) && attempts >= FALLBACK_CONFIG.retryAttempts) {
                return Promise.reject(new Error(`Max retries exceeded for ${src}`));
            }

            try {
                await this.loadScript(src);
                this.loadedScripts.add(src);
                this.retryCount.delete(retryKey);
                return true;
            } catch (error) {
                attempts++;
                this.retryCount.set(retryKey, attempts);

                if (attempts < FALLBACK_CONFIG.retryAttempts) {
                    const delay = FALLBACK_CONFIG.retryDelay * Math.pow(2, attempts - 1);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return this.loadScriptWithRetry(src);
                } else {
                    this.failedScripts.add(src);
                    throw error;
                }
            }
        }

        async loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;

                const timeout = setTimeout(() => {
                    reject(new Error(`Script load timeout: ${src}`));
                }, FALLBACK_CONFIG.timeout);

                script.onload = () => {
                    clearTimeout(timeout);
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error(`Failed to load script: ${src}`));
                };

                document.head.appendChild(script);
            });
        }

        async waitForDependencies() {
            const promises = FALLBACK_CONFIG.criticalDependencies.map(dep => 
                this.waitForDependency(dep)
            );
            
            const results = await Promise.allSettled(promises);
            
            results.forEach((result, index) => {
                if (result.status === 'rejected') {
                    console.warn(`Critical dependency not available: ${FALLBACK_CONFIG.criticalDependencies[index]}`);
                }
            });
        }

        async waitForDependency(dependencyPath) {
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error(`Dependency timeout: ${dependencyPath}`));
                }, FALLBACK_CONFIG.timeout);

                const check = () => {
                    try {
                        const value = this.getNestedProperty(window, dependencyPath.replace('window.', ''));
                        if (value) {
                            clearTimeout(timeout);
                            resolve(value);
                        } else {
                            setTimeout(check, 100);
                        }
                    } catch (error) {
                        setTimeout(check, 100);
                    }
                };

                check();
            });
        }

        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : null;
            }, obj);
        }

        async initializeBasics() {
            try {
                // Initialize WebSocket connections
                if (window.WebSocketManager) {
                    if (!window.bitstampWsManager) {
                        window.bitstampWsManager = new WebSocketManager(
                            'wss://ws.bitstamp.net',
                            'bitstamp',
                            { reconnectDelay: 2000 }
                        );
                    }

                    if (!window.bybitWsManager) {
                        window.bybitWsManager = new WebSocketManager(
                            'wss://stream.bybit.com/v5/public/linear',
                            'bybit',
                            { reconnectDelay: 2000 }
                        );
                    }
                }

                // Initialize basic chart state
                if (!window.chartStates) {
                    window.chartStates = new Map();
                    window.currentPair = 'BTC';
                    window.currentActivePair = 'BTC';
                }

                // Set up basic event listeners
                this.setupBasicEventListeners();

            } catch (error) {
                console.warn('Basic initialization partial failure:', error);
            }
        }

        setupBasicEventListeners() {
            // Basic pair button functionality
            document.addEventListener('click', (event) => {
                const button = event.target.closest('.pair-button');
                if (button && button.dataset.pair) {
                    this.handlePairSwitch(button.dataset.pair);
                }
            });

            // Handle visibility changes
            document.addEventListener('visibilitychange', () => {
                if (!document.hidden && window.bitstampWsManager) {
                    window.bitstampWsManager.reconnect?.();
                }
            });
        }

        handlePairSwitch(newPair) {
            // Update button states
            document.querySelectorAll('.pair-button').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.pair === newPair);
            });

            // Update global state
            window.currentPair = newPair;
            window.currentActivePair = newPair;

            // Update container
            const container = document.querySelector('.chart-container');
            if (container) {
                container.dataset.pair = newPair;
            }

            // Notify other components
            document.dispatchEvent(new CustomEvent('pairChanged', {
                detail: { pair: newPair }
            }));
        }

        async loadRemainingModules() {
            const remainingScripts = [
                'modules/consoleCapture.js',
                'indicators/utils.js',
                'indicators/cvd.js',
                'indicators/indicators.js'
            ];

            // Load non-essential scripts in background
            setTimeout(async () => {
                for (const script of remainingScripts) {
                    try {
                        await this.loadScriptWithRetry(script);
                    } catch (error) {
                        console.warn(`Optional script failed: ${script}`);
                    }
                }
            }, 1000);
        }

        createBasicUI() {
            // Ensure basic elements exist
            const mainContainer = document.querySelector('.main-container');
            if (!mainContainer) {
                const container = document.createElement('div');
                container.className = 'main-container';
                container.innerHTML = `
                    <div class="order-books-container">
                        <div class="loading-message">Loading order books...</div>
                    </div>
                    <div class="charts-container">
                        <div class="chart-container">
                            <div class="pair-selector">
                                <button class="pair-button active" data-pair="BTC">BTC</button>
                                <button class="pair-button" data-pair="ETH">ETH</button>
                                <button class="pair-button" data-pair="LTC">LTC</button>
                                <button class="pair-button" data-pair="SOL">SOL</button>
                            </div>
                            <div class="price-chart-container">
                                <div class="loading-overlay">Loading chart...</div>
                                <div class="price-chart"></div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(container);
            }
        }

        async initializeMinimalWebSocket() {
            // Create a simple WebSocket connection for basic functionality
            try {
                const ws = new WebSocket('wss://ws.bitstamp.net');
                
                ws.onopen = () => {
                    console.log('Minimal WebSocket connected');
                    // Subscribe to BTC order book
                    ws.send(JSON.stringify({
                        event: 'bts:subscribe',
                        data: { channel: 'order_book_btcusd' }
                    }));
                };

                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.channel === 'order_book_btcusd') {
                            this.updateMinimalOrderBook(data.data);
                        }
                    } catch (error) {
                        console.warn('WebSocket message parsing error:', error);
                    }
                };

                ws.onerror = () => {
                    console.warn('Minimal WebSocket connection failed');
                };

            } catch (error) {
                console.warn('Could not establish minimal WebSocket:', error);
            }
        }

        updateMinimalOrderBook(data) {
            // Very basic order book display
            const container = document.querySelector('.loading-message');
            if (container && data.bids && data.asks) {
                const topBid = data.bids[0];
                const topAsk = data.asks[0];
                if (topBid && topAsk) {
                    container.innerHTML = `
                        <div>BTC/USD</div>
                        <div>Bid: $${parseFloat(topBid[0]).toFixed(2)}</div>
                        <div>Ask: $${parseFloat(topAsk[0]).toFixed(2)}</div>
                    `;
                }
            }
        }

        showLimitedMessage() {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(255, 193, 7, 0.9);
                color: #000;
                padding: 10px 20px;
                border-radius: 4px;
                z-index: 10000;
                font-weight: bold;
            `;
            message.textContent = 'Running in limited mode - Some features may not be available';
            document.body.appendChild(message);

            setTimeout(() => {
                message.remove();
            }, 8000);
        }

        notifyReady(success = true) {
            const loadingTime = Date.now() - this.startTime;
            console.log(`Fallback initialization completed in ${loadingTime}ms`);

            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('dashboardReady', {
                detail: { 
                    success, 
                    loadingTime,
                    mode: 'fallback'
                }
            }));

            // Mark body as ready
            document.body.classList.add('dashboard-ready');
            if (!success) {
                document.body.classList.add('limited-mode');
            }
        }

        // Public API
        getLoadedScripts() {
            return Array.from(this.loadedScripts);
        }

        getFailedScripts() {
            return Array.from(this.failedScripts);
        }

        isReady() {
            return this.isInitialized;
        }
    }

    // Initialize fallback system
    window.FallbackInitializer = FallbackInitializer;

    // Auto-start fallback if main loading hasn't started within timeout
    setTimeout(() => {
        if (!window.loadingManager && !window.fallbackInitializer) {
            console.log('Main loading system not detected, starting fallback');
            window.fallbackInitializer = new FallbackInitializer();
            window.fallbackInitializer.initialize();
        }
    }, 2000);

    // Register cleanup
    if (window.CleanupManager?.registerCleanup) {
        window.CleanupManager.registerCleanup(() => {
            if (window.fallbackInitializer) {
                window.fallbackInitializer = null;
            }
        });
    }

})();