(function(window) {
    // Store original console methods
    const originalMethods = {
        warn: console.warn,
        error: console.error,
        log: console.log
    };

    // Compile regex patterns once for better performance
    const filterRegex = /\[Violation\]|ResizeObserver|Resizing chart to|Container dimensions|Candlestick data set successfully|Chart fitted to content|Using cached data|Cached \d+ bars|Received \d+ bars|Restored \d+ drawings|Cleanup completed|No CVD pane available|CVD pane not available|Creating perpImbalance|Number of panes|Using second pane|Creating.*series|Using pane index|Successfully created|Using provided OI data|Spot data length|Futures data length|OI data length|Setting perpImbalance data|Drawing functionality enabled|Performing secondary initialization|Updating chart with fresh data/i;

    const keepRegex = /WebSocket connected successfully|Charts\.js loaded successfully|ConsoleCapture\.js loaded successfully|Error/i;

    // Cache for regex results to avoid recomputation
    const regexCache = new Map();
    const maxCacheSize = 1000;

    // Single efficient filter function with caching
    const shouldFilter = (message) => {
        if (typeof message !== 'string') return false;

        // Check cache first
        if (regexCache.has(message)) {
            return regexCache.get(message);
        }

        const result = filterRegex.test(message) && !keepRegex.test(message);

        // Manage cache size
        if (regexCache.size >= maxCacheSize) {
            const firstKey = regexCache.keys().next().value;
            regexCache.delete(firstKey);
        }

        regexCache.set(message, result);
        return result;
    };

    // Apply filtering to all console methods
    ['warn', 'error', 'log'].forEach(method => {
        console[method] = (...args) => {
            if (args.length > 0 && !shouldFilter(args[0])) {
                originalMethods[method].apply(console, args);
            }
        };
    });

    // Optionally expose for debugging
    window.consoleFilter = {
        shouldFilter
    };
})(window);