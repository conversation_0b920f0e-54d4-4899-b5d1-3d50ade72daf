(function() {
    'use strict';

    // Simple, dependency-free loading manager
    const SimpleLoader = {
        progress: 0,
        isLoading: true,
        loadedScripts: new Set(),
        progressBar: null,
        statusText: null,

        init() {
            this.startTime = Date.now();
            this.createProgressUI();
            this.startLoading();
        },

        createProgressUI() {
            // Create simple progress bar
            this.progressBar = document.createElement('div');
            this.progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: linear-gradient(90deg, #26a69a, #4db6ac);
                z-index: 10001;
                transition: width 0.3s ease-out;
                box-shadow: 0 0 10px rgba(38, 166, 154, 0.6);
            `;

            // Create status indicator
            this.statusText = document.createElement('div');
            this.statusText.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: #26a69a;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 0.8rem;
                z-index: 10001;
                opacity: 0.9;
                backdrop-filter: blur(4px);
            `;
            this.statusText.textContent = 'Loading...';

            document.body.appendChild(this.progressBar);
            document.body.appendChild(this.statusText);
        },

        updateProgress(percent, message) {
            this.progress = Math.min(100, percent);
            if (this.progressBar) {
                this.progressBar.style.width = this.progress + '%';
            }
            if (this.statusText && message) {
                this.statusText.textContent = message;
            }

            if (this.progress >= 100) {
                setTimeout(() => this.complete(), 500);
            }
        },

        loadScript(src) {
            return new Promise((resolve, reject) => {
                if (this.loadedScripts.has(src)) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = src;
                script.async = false; // Maintain order

                script.onload = () => {
                    this.loadedScripts.add(src);
                    resolve();
                };

                script.onerror = () => {
                    console.warn('Failed to load:', src);
                    resolve(); // Continue even if script fails
                };

                document.head.appendChild(script);
            });
        },

        async loadLightweightCharts() {
            return new Promise((resolve, reject) => {
                // Check if already loaded
                if (window.LightweightCharts) {
                    resolve();
                    return;
                }

                // Load from CDN with retry mechanism (used as fallback)
                const loadFromCDN = (attempt = 1) => {
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.js';
                    script.async = false;

                    script.onload = () => {
                        this.loadedScripts.add(script.src);
                        resolve();
                    };

                    script.onerror = () => {
                        if (attempt < 3) {
                            console.warn(`Failed to load LightweightCharts from CDN (attempt ${attempt}), retrying...`);
                            setTimeout(() => loadFromCDN(attempt + 1), 1000);
                        } else {
                            // Try alternative CDN as fallback
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = 'https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js';
                            fallbackScript.async = false;

                            fallbackScript.onload = () => {
                                this.loadedScripts.add(fallbackScript.src);
                                resolve();
                            };

                            fallbackScript.onerror = () => {
                                console.error('Failed to load LightweightCharts from all CDNs');
                                // Continue anyway to prevent blocking the app
                                resolve();
                            };

                            document.head.appendChild(fallbackScript);
                        }
                    };

                    document.head.appendChild(script);
                };

                // First, try to use the preloaded resources
                const preloadedLinks = Array.from(document.querySelectorAll('link[rel="preload"][as="script"]'))
                    .filter(link => link.href.includes('lightweight-charts'));

                if (preloadedLinks.length > 0) {
                    // Use the first preloaded resource
                    const script = document.createElement('script');
                    script.src = preloadedLinks[0].href;
                    script.async = false;

                    script.onload = () => {
                        this.loadedScripts.add(script.src);
                        resolve();
                    };

                    script.onerror = () => {
                        // If the preloaded resource fails, try the fallback
                        if (preloadedLinks.length > 1) {
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = preloadedLinks[1].href;
                            fallbackScript.async = false;

                            fallbackScript.onload = () => {
                                this.loadedScripts.add(fallbackScript.src);
                                resolve();
                            };

                            fallbackScript.onerror = () => {
                                console.error('Failed to load LightweightCharts from preloaded resources');
                                // Continue anyway to prevent blocking the app
                                resolve();
                            };

                            document.head.appendChild(fallbackScript);
                        } else {
                            // If no fallback preloaded resource, use the CDN directly
                            loadFromCDN();
                        }
                    };

                    document.head.appendChild(script);
                } else {
                    // If no preloaded resources, use the CDN directly
                    loadFromCDN();
                }
            });
        },

        async startLoading() {
            try {
                // Phase 1: Essential scripts and chart library
                this.updateProgress(10, 'Loading core utilities...');
                await Promise.all([
                    this.loadLightweightCharts(),
                    this.loadScript('utils/commonUtils.js'),
                    this.loadScript('utils/mathUtils.js'),
                    this.loadScript('utils/config.js'),
                    this.loadScript('utils/dataFetcher.js') // Added dataFetcher.js
                ]);

                // Phase 2: WebSocket manager
                this.updateProgress(30, 'Loading WebSocket manager...');
                await this.loadScript('wsmanager.js');

                // Phase 3: Core modules
                this.updateProgress(50, 'Loading chart modules...');
                await this.loadScript('modules/orderbook.js');

                // Wait for LightweightCharts to be fully available
                await this.waitForModule('LightweightCharts', 10000);

                // Load IndexedDbWrapper before charts.js to ensure caching works
                await this.loadScript('utils/db/indexedDbWrapper.js');
                // Now load charts.js after ensuring LightweightCharts is available
                await this.loadScript('modules/charts.js');

                // Phase 4: Console and UI
                this.updateProgress(70, 'Loading UI components...');
                await this.loadScript('modules/consoleCapture.js');

                // Phase 5: Indicators (essential for charts)
                this.updateProgress(80, 'Loading indicators...');
                await this.loadScript('indicators/utils.js');

                // Wait for utils to be available before loading dependent modules
                await this.waitForModule('utils');

                // Load shared utilities
                await this.loadScript('utils/shared/indicatorChartUtils.js');
                await this.loadScript('utils/shared/profiles/baseProfile.js');

                // Load utility managers (skip problematic ES6 modules and performanceOptimizer)
                await this.loadScript('utils/chartSwitcher.js');

                // Load data stores first (required by indicators)
                await this.loadScript('indicators/data/cvdDataStore.js');
                await this.loadScript('indicators/data/perpCvdDataStore.js');

                // Verify PERP CVD data store loaded correctly
                if (!window.PS || !window.PS.setPerpCVDPriceData) {
                    console.warn('[SimpleLoader] PERP CVD Data Store failed to load, retrying...');
                    await this.loadScript('indicators/data/perpCvdDataStore.js');

                    if (!window.PS || !window.PS.setPerpCVDPriceData) {
                        console.error('[SimpleLoader] PERP CVD Data Store still not available after retry');
                    }
                } else {
                    // Check if we have existing chart data to inject
                    setTimeout(() => {
                        if (window.chartStates) {
                            const btcState = window.chartStates.get('BTC');
                            if (btcState && btcState.data && btcState.data.alignedBybit && btcState.data.alignedBybit.length > 0) {
                                window.PS.setPerpCVDPriceData(btcState.data.alignedBybit.slice());
                            }
                        }
                    }, 1000); // Wait 1 second for chart data to be available
                }

                await this.loadScript('indicators/data/perpImbalanceDataStore.js');

                // Load indicator modules
                await this.loadScript('indicators/cvd.js');
                await this.loadScript('indicators/perpcvd.js');
                await this.loadScript('indicators/perpimbalance.js');
                await this.loadScript('indicators/bybitNetFlow.js');
                await this.loadScript('indicators/indicators.js');

                // Load popup chart module
                await this.loadScript('modules/popup-chart.js');

                // Verify critical modules loaded
                await this.verifyModules();

                // Phase 6: Complete
                this.updateProgress(100, 'Dashboard ready!');

            } catch (error) {
                console.error('Loading error:', error);
                this.updateProgress(100, 'Loading completed with errors');
            }
        },

        waitForModule(moduleName, timeout = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkModule = () => {
                    if (window[moduleName]) {
                        // Add a small delay to ensure the module is fully initialized
                        setTimeout(() => resolve(), 50);
                    } else if (Date.now() - startTime < timeout) {
                        setTimeout(checkModule, 100);
                    } else {
                        console.warn(`Module ${moduleName} not loaded within timeout`);

                        // For critical modules like LightweightCharts, try to load a fallback
                        if (moduleName === 'LightweightCharts') {
                            console.warn('Attempting to load fallback for LightweightCharts...');
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = 'https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js';
                            fallbackScript.onload = () => {
                                console.log('Fallback LightweightCharts loaded successfully');
                                setTimeout(() => resolve(), 100);
                            };
                            fallbackScript.onerror = () => {
                                console.error('Failed to load fallback LightweightCharts');
                                resolve(); // Continue anyway
                            };
                            document.head.appendChild(fallbackScript);
                        } else {
                            resolve(); // Continue anyway for non-critical modules
                        }
                    }
                };
                checkModule();
            });
        },

        async verifyModules() {
            const requiredModules = ['LightweightCharts', 'cvdModule', 'perpCvdModule', 'perpImbalance', 'IndicatorChartUtils', 'BaseProfile', 'popupChart', 'chartSwitcher'];
            const requiredDataStoreFunctions = [
                'subscribeCVD', 'subscribePerpCVD', 'subscribePerpImbalance',
                'getCurrentPerpCVD', 'getCurrentPerpImbalance', 
                'setPerpCVD', 'setPerpImbalance', 'addBybitTrade', 'resetNetFlow'
            ];
            const missingModules = [];
            const missingDataStoreFunctions = [];

            for (const module of requiredModules) {
                if (!window[module]) {
                    missingModules.push(module);
                }
            }

            for (const func of requiredDataStoreFunctions) {
                if (!window.PS || typeof window.PS[func] !== 'function') {
                    missingDataStoreFunctions.push(func);
                }
            }

            if (missingModules.length > 0) {
                console.warn('Missing modules:', missingModules);
                // Add stub modules to prevent errors
                this.addMissingModuleStubs(missingModules);
            }

            if (missingDataStoreFunctions.length > 0) {
                console.warn('Missing data store functions:', missingDataStoreFunctions);
                // CVD stubs will handle these
                this.addCVDStubs();
            }

            // Always add general stubs for essential components
            this.addGeneralStubs();
        },

        addMissingModuleStubs(missingModules) {
            missingModules.forEach(moduleName => {
                if (moduleName === 'LightweightCharts') {
                    // Create a minimal LightweightCharts stub to prevent errors
                    window.LightweightCharts = {
                        createChart: (container, options) => ({
                            resize: () => {},
                            remove: () => {},
                            timeScale: () => ({
                                fitContent: () => {},
                                scrollToRealTime: () => {},
                                setVisibleRange: () => {},
                                getVisibleLogicalRange: () => ({ from: 0, to: 100 }),
                                subscribeVisibleLogicalRangeChange: (handler) => ({ remove: () => {} })
                            }),
                            addSeries: (seriesType, options) => ({
                                setData: () => {},
                                update: () => {},
                                createPriceLine: () => ({ applyOptions: () => {}, remove: () => {} }),
                                dataByIndex: () => []
                            }),
                            subscribeCrosshairMove: () => ({ remove: () => {} }),
                            applyOptions: () => {},
                            panes: () => [{}],
                            removeSeries: () => {}
                        }),
                        CandlestickSeries: 'candlestick',
                        LineSeries: 'line',
                        HistogramSeries: 'histogram',
                        CrosshairMode: { Normal: 'normal', Hidden: 'hidden' },
                        TrackingModeExitMode: { OnNextTap: 'on-next-tap' },
                        PriceLineSource: { LastBar: 'last-bar' }
                    };
                    console.warn('Created LightweightCharts stub - charts will not render properly');
                } else if (moduleName === 'cvdModule') {
                    window.cvdModule = {
                        createCVDChart: () => ({ series: null, syncResources: null }),
                        synchronizeCharts: () => ({}),
                        initializeCVDData: () => {},
                        setupCVDUpdateInterval: () => {},
                        updateCVD: () => {},
                        renderPendingCVDUpdates: () => {},
                        resizeCVDChart: () => {},
                        cleanupCVD: () => {}
                    };
                } else if (moduleName === 'perpCvdModule') {
                    window.perpCvdModule = {
                        createCVDChart: () => ({ series: null, syncResources: null }),
                        synchronizeCharts: () => ({}),
                        initializeCVDData: () => {},
                        setupCVDUpdateInterval: () => {},
                        updateCVD: () => {},
                        renderPendingCVDUpdates: () => {},
                        resizeCVDChart: () => {},
                        cleanupCVD: () => {}
                    };
                } else if (moduleName === 'perpImbalance') {
                    window.perpImbalance = {
                        createPerpImbalanceIndicator: () => ({ series: null, syncResources: null }),
                        initializeImbalanceData: () => {},
                        synchronizeCharts: () => ({}),
                        updateImbalance: () => {},
                        renderPendingUpdates: () => {},
                        resizeIndicator: () => {},
                        cleanupIndicator: () => {},
                        disableCrosshairMarkers: () => {}
                    };
                } else if (moduleName === 'popupChart') {
                    window.popupChart = {
                        chart: null,
                        series: null,
                        initialize: () => {},
                        cleanup: () => {},
                        loadChartData: () => {},
                        updateTimeframe: () => {}
                    };
                }
//                console.log(`Added stub for missing module: ${moduleName}`);
            });
        },

        complete() {
            this.isLoading = false;

            // Initialize basic functionality
            this.initializeBasics();

            // Hide progress indicators
            if (this.progressBar) {
                this.progressBar.style.opacity = '0';
                setTimeout(() => {
                    if (this.progressBar.parentNode) {
                        this.progressBar.parentNode.removeChild(this.progressBar);
                    }
                }, 300);
            }

            if (this.statusText) {
                this.statusText.style.opacity = '0';
                setTimeout(() => {
                    if (this.statusText.parentNode) {
                        this.statusText.parentNode.removeChild(this.statusText);
                    }
                }, 300);
            }

            // Mark as ready
            document.body.classList.add('dashboard-ready');

            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('dashboardReady', {
                detail: { 
                    success: true, 
                    mode: 'simple',
                    loadingTime: Date.now() - (this.startTime || Date.now()),
                    modulesLoaded: {
                        cvdModule: !!window.cvdModule,
                        perpCvdModule: !!window.perpCvdModule,
                        perpImbalance: !!window.perpImbalance
                    }
                }
            }));
        },

        initializeBasics() {
            // Set up basic chart states
            if (!window.chartStates) {
                window.chartStates = new Map();
            }
            if (!window.currentPair) {
                window.currentPair = 'BTC';
                window.currentActivePair = 'BTC';
            }

            // Initialize WebSocket connections if available
            if (window.WebSocketManager) {
                if (!window.bitstampWsManager) {
                    try {
                        window.bitstampWsManager = new WebSocketManager(
                            'wss://ws.bitstamp.net',
                            'bitstamp',
                            { reconnectDelay: 2000 }
                        );
                    } catch (error) {
                        console.warn('Failed to initialize Bitstamp WebSocket:', error);
                    }
                }

                if (!window.bybitWsManager) {
                    try {
                        window.bybitWsManager = new WebSocketManager(
                            'wss://stream.bybit.com/v5/public/linear',
                            'bybit',
                            { reconnectDelay: 2000 }
                        );
                    } catch (error) {
                        console.warn('Failed to initialize Bybit WebSocket:', error);
                    }
                }
            }

            // Set up basic event handlers
            this.setupEventHandlers();

            // Add missing CVD subscription stub
            this.addCVDStubs();
        },

        setupEventHandlers() {
            // Basic pair button functionality with smooth switching
            document.addEventListener('click', (event) => {
                const target = event.target;
                if (target.classList.contains('pair-button') && target.dataset.pair) {
                    this.switchToPair(target.dataset.pair);
                }
            });
        },

        switchToPair(newPair) {
            if (window.currentPair === newPair) return;

            // Show loading state
            const chartContainer = document.querySelector('.price-chart-container');
            if (chartContainer) {
                const loadingOverlay = chartContainer.querySelector('.loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'flex';
                    loadingOverlay.textContent = `Loading ${newPair} data...`;
                }

                // Add fade effect
                chartContainer.style.opacity = '0.7';
                setTimeout(() => {
                    chartContainer.style.opacity = '1';
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                }, 800);
            }

            // Update active states
            document.querySelectorAll('.pair-button').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.pair === newPair);
            });

            // Update global state
            window.currentPair = newPair;
            window.currentActivePair = newPair;

            // Update container
            const container = document.querySelector('.chart-container');
            if (container) {
                container.dataset.pair = newPair;
            }

            // Call existing chart switching if available
            if (window.switchPairInternal) {
                window.switchPairInternal(newPair);
            } else if (window.switchPair) {
                window.switchPair(newPair);
            }

            // Reset net flow if available
            if (window.PS && window.PS.resetNetFlow) {
                window.PS.resetNetFlow();
            }

            // Dispatch event
            document.dispatchEvent(new CustomEvent('pairChanged', {
                detail: { pair: newPair }
            }));

            console.log(`Switched to ${newPair}`);
        },

        addCVDStubs() {
            // Add missing CVD functions to prevent errors
            window.PS = window.PS || {};
            if (!window.PS.subscribeCVD) {
                window.PS.subscribeCVD = function(callback) {
                    console.log(`CVD subscription for ${callback} - stub function`);
                    return function() { console.log('CVD unsubscribe - stub'); };
                };
            }

            if (!window.unsubscribeCVD) {
                window.unsubscribeCVD = function(pair) {
                    console.log(`CVD unsubscription for ${pair} - stub function`);
                    return true;
                };
            }

            if (!window.updateCVD) {
                window.updateCVD = function(data) {
                    console.log('CVD update - stub function', data);
                    return true;
                };
            }

            // Add PerpCVD data store stubs
            if (!window.PS.subscribePerpCVD) {
                window.PS.subscribePerpCVD = function(callback) {
                    console.log(`PerpCVD subscription - stub function`);
                    return function() { console.log('PerpCVD unsubscribe - stub'); };
                };
            }

            if (!window.PS.getCurrentPerpCVD) {
                window.PS.getCurrentPerpCVD = function() {
                    return { time: Date.now() / 1000, value: 0 };
                };
            }

            if (!window.PS.setPerpCVD) {
                window.PS.setPerpCVD = function(data) {
                    console.log('PerpCVD set - stub function', data);
                };
            }

            // Add PerpImbalance data store stubs
            if (!window.subscribePerpImbalance) {
                window.subscribePerpImbalance = function(callback) {
                    console.log(`PerpImbalance subscription - stub function`);
                    return function() { console.log('PerpImbalance unsubscribe - stub'); };
                };
            }

            if (!window.getCurrentPerpImbalance) {
                window.getCurrentPerpImbalance = function() {
                    return { time: Date.now() / 1000, value: 0 };
                };
            }

            if (!window.PS.setPerpImbalance) {
                window.PS.setPerpImbalance = function(data) {
                    console.log('PerpImbalance set - stub function', data);
                };
            }

            // Add bybitNetFlow stubs
            if (!window.PS.addBybitTrade) {
                window.PS.addBybitTrade = function(trade) {
                    console.log('addBybitTrade - stub function', trade);
                };
            }

            if (!window.PS.resetNetFlow) {
                window.PS.resetNetFlow = function() {
                    console.log('resetNetFlow - stub function');
                };
            }

            // Add IndicatorChartUtils stub
            if (!window.IndicatorChartUtils) {
                window.IndicatorChartUtils = {
                    synchronizeCharts: function(components, priceChart, options) {
                        console.log('IndicatorChartUtils.synchronizeCharts - stub function');
                        return { unsubscribe: function() { console.log('IndicatorChartUtils unsubscribe - stub'); } };
                    },
                    cleanupIndicator: function(components, intervals, resetState) {
                        console.log('IndicatorChartUtils.cleanupIndicator - stub function');
                        if (intervals && Array.isArray(intervals)) {
                            intervals.forEach(interval => {
                                if (interval && typeof interval === 'number') {
                                    clearInterval(interval);
                                }
                            });
                        }
                    }
                };
            }
        },

        addGeneralStubs() {
            // Add BaseProfile stub if not loaded
            if (!window.BaseProfile) {
                window.BaseProfile = function() {
                    return {
                        padRange: function(min, max, percentage) {
                            const padding = (max - min) * percentage;
                            return { min: min - padding, max: max + padding };
                        }
                    };
                };
            }

            // Add CleanupManager stub if not loaded
            if (!window.CleanupManager) {
                let cleanupStubLogged = false;
                window.CleanupManager = {
                    registerCleanup: function(fn) {
                        // No logging or warnings; stub is silent
                    },
                    runAllCleanups: function() { /* console.log('CleanupManager.runAllCleanups - stub'); */ },
                    getCleanupCount: function() { return 0; }
                };
            }

            // Add chartSwitcher stub if not loaded
            if (!window.chartSwitcher) {
                window.chartSwitcher = {
                    switchTo: function(pair) { /* console.log('chartSwitcher.switchTo - stub', pair); */ },
                    getStats: function() { return {}; },
                    clearCache: function() { /* console.log('chartSwitcher.clearCache - stub'); */ }
                };
            }

            // Add performanceOptimizer stub if not loaded
            if (!window.performanceOptimizer) {
                window.performanceOptimizer = {
                    init: function() { /* no-op stub */ },
                    getMetrics: function() { return { longTaskCount: 0, cls: 0 }; },
                    cleanup: function() { /* no-op stub */ }
                };
            }

            // Add eventBus stub if not loaded
            if (!window.eventBus) {
                window.eventBus = {
                    events: {},
                    subscribe: function(event, callback) {
                        window.eventBus.events[event] = window.eventBus.events[event] || [];
                        window.eventBus.events[event].push(callback);
                        return function() {
                            window.eventBus.events[event] = window.eventBus.events[event].filter(cb => cb !== callback);
                        };
                    },
                    publish: function(event, data) {
                        if (window.eventBus.events[event]) {
                            window.eventBus.events[event].forEach(callback => {
                                try {
                                    callback(data);
                                } catch (err) {
                                    console.error(`Error in ${event}:`, err);
                                }
                            });
                        }
                    }
                };
            }

            // Add chartOrderbook stub if not loaded
            if (!window.chartOrderbook) {
                window.chartOrderbook = {
                    updateOrderBookLines: function(state) {
                        // console.log('chartOrderbook.updateOrderBookLines - stub');
                    },
                    clearOrderBookLines: function(state) {
                        // console.log('chartOrderbook.clearOrderBookLines - stub');
                    }
                };
            }
        }
    };

    // Auto-start if not already loading
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            if (!window.loadingManager && !document.body.classList.contains('dashboard-ready')) {
                window.simpleLoader = SimpleLoader;
                window.simpleLoader.init();
            }
        });
    } else {
        if (!window.loadingManager && !document.body.classList.contains('dashboard-ready')) {
            window.simpleLoader = SimpleLoader;
            window.simpleLoader.init();
        }
    }

})();
