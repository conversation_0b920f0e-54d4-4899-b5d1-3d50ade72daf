/**
 * Advanced Loading Manager
 * Provides smooth page loading, progress tracking, and optimized resource management
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        progressSteps: {
            SCRIPTS: 30,
            WEBSOCKETS: 20,
            CHARTS: 25,
            INDICATORS: 15,
            PROFILES: 10
        },
        animations: {
            fadeInDuration: 300,
            fadeOutDuration: 200,
            progressBarSpeed: 50
        },
        timeouts: {
            scriptLoad: 10000,
            websocketConnect: 5000,
            chartInit: 3000
        },
        retries: {
            maxAttempts: 3,
            delay: 1000
        }
    };

    class LoadingManager {
        constructor() {
            this.progress = 0;
            this.currentStep = '';
            this.isLoading = true;
            this.loadedResources = new Set();
            this.failedResources = new Set();
            this.resourceCache = new Map();
            this.progressCallbacks = [];
            this.completionCallbacks = [];
            this.retryAttempts = new Map();
            
            this.init();
        }

        init() {
            this.createLoadingUI();
            this.createSimpleProgressIndicator();
            this.setupEventListeners();
            this.preloadCriticalResources();
        }

        createLoadingUI() {
            // Create loading overlay
            this.overlay = document.createElement('div');
            this.overlay.id = 'loading-manager-overlay';
            this.overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: linear-gradient(135deg, #050a0f 0%, #0a1118 50%, #0f1a25 100%);
                z-index: 10000;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                transition: opacity ${CONFIG.animations.fadeOutDuration}ms ease-out;
            `;

            // Create logo/title
            this.title = document.createElement('div');
            this.title.textContent = 'Crypto Dashboard';
            this.title.style.cssText = `
                font-size: 2.5rem;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 2rem;
                text-align: center;
                font-family: 'Arial', sans-serif;
                letter-spacing: 2px;
                opacity: 0;
                animation: fadeInSlide 1s ease-out 0.2s forwards;
            `;

            // Create progress container
            this.progressContainer = document.createElement('div');
            this.progressContainer.style.cssText = `
                width: 400px;
                max-width: 80vw;
                margin-bottom: 1rem;
                opacity: 0;
                animation: fadeInSlide 1s ease-out 0.5s forwards;
            `;

            // Create progress bar
            this.progressBar = document.createElement('div');
            this.progressBar.style.cssText = `
                width: 100%;
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                overflow: hidden;
                position: relative;
            `;

            this.progressFill = document.createElement('div');
            this.progressFill.style.cssText = `
                height: 100%;
                background: linear-gradient(90deg, #26a69a, #4db6ac, #80cbc4);
                border-radius: 2px;
                width: 0%;
                transition: width ${CONFIG.animations.progressBarSpeed}ms ease-out;
                box-shadow: 0 0 10px rgba(38, 166, 154, 0.5);
            `;

            this.progressBar.appendChild(this.progressFill);

            // Create progress text
            this.progressText = document.createElement('div');
            this.progressText.style.cssText = `
                color: #cccccc;
                font-size: 0.9rem;
                text-align: center;
                margin-top: 0.5rem;
                min-height: 1.2rem;
            `;

            // Create status text
            this.statusText = document.createElement('div');
            this.statusText.style.cssText = `
                color: #999999;
                font-size: 0.8rem;
                text-align: center;
                margin-top: 0.5rem;
                min-height: 1rem;
                opacity: 0;
                animation: fadeInSlide 1s ease-out 0.8s forwards;
            `;

            // Create CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInSlide {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.7; }
                }
                .loading-pulse {
                    animation: pulse 2s ease-in-out infinite;
                }
            `;
            document.head.appendChild(style);

            // Assemble UI
            this.progressContainer.appendChild(this.progressBar);
            this.progressContainer.appendChild(this.progressText);
            
            this.overlay.appendChild(this.title);
            this.overlay.appendChild(this.progressContainer);
            this.overlay.appendChild(this.statusText);

            document.body.appendChild(this.overlay);
        }

        createSimpleProgressIndicator() {
            // Create simple progress bar for top of page
            this.simpleProgressBar = document.createElement('div');
            this.simpleProgressBar.className = 'loading-progress-bar';
            this.simpleProgressBar.innerHTML = '<div class="loading-progress-fill"></div>';
            
            this.simpleProgressFill = this.simpleProgressBar.querySelector('.loading-progress-fill');
            
            // Create status indicator
            this.simpleStatusIndicator = document.createElement('div');
            this.simpleStatusIndicator.className = 'loading-status-indicator';
            this.simpleStatusIndicator.textContent = 'Loading...';
            
            // Insert at beginning of body
            if (document.body) {
                document.body.insertBefore(this.simpleProgressBar, document.body.firstChild);
                document.body.appendChild(this.simpleStatusIndicator);
            } else {
                // Wait for body to be available
                document.addEventListener('DOMContentLoaded', () => {
                    document.body.insertBefore(this.simpleProgressBar, document.body.firstChild);
                    document.body.appendChild(this.simpleStatusIndicator);
                });
            }
        }

        setupEventListeners() {
            // Handle page visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && this.isLoading) {
                    this.pauseLoading();
                } else if (!document.hidden && this.isLoading) {
                    this.resumeLoading();
                }
            });

            // Handle window focus/blur
            window.addEventListener('focus', () => {
                if (this.isLoading) this.resumeLoading();
            });

            window.addEventListener('blur', () => {
                if (this.isLoading) this.pauseLoading();
            });
        }

        preloadCriticalResources() {
            const criticalResources = [
                'https://unpkg.com/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.js',
                'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js'
            ];

            criticalResources.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'script';
                link.href = url;
                document.head.appendChild(link);
            });
        }

        updateProgress(increment, stepName, detail = '') {
            this.progress = Math.min(100, this.progress + increment);
            this.currentStep = stepName;

            // Smooth progress animation
            requestAnimationFrame(() => {
                // Update main overlay
                if (this.progressFill) {
                    this.progressFill.style.width = `${this.progress}%`;
                }
                if (this.progressText) {
                    this.progressText.textContent = `${Math.round(this.progress)}%`;
                }
                if (this.statusText) {
                    this.statusText.textContent = detail || stepName;
                }
                
                // Update simple indicators
                if (this.simpleProgressFill) {
                    this.simpleProgressFill.style.width = `${this.progress}%`;
                    this.simpleProgressBar.classList.add('visible');
                }
                if (this.simpleStatusIndicator) {
                    this.simpleStatusIndicator.textContent = detail || stepName;
                    this.simpleStatusIndicator.classList.add('visible');
                }
            });

            // Notify callbacks
            this.progressCallbacks.forEach(callback => {
                try {
                    callback(this.progress, stepName, detail);
                } catch (error) {
                    console.warn('Progress callback error:', error);
                }
            });

            // Check completion
            if (this.progress >= 100 && this.isLoading) {
                setTimeout(() => this.completeLoading(), 500);
            }
        }

        async loadScript(src, name, timeout = CONFIG.timeouts.scriptLoad) {
            if (this.loadedResources.has(src)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;

                const timeoutId = setTimeout(() => {
                    reject(new Error(`Script load timeout: ${name}`));
                }, timeout);

                script.onload = () => {
                    clearTimeout(timeoutId);
                    this.loadedResources.add(src);
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timeoutId);
                    this.failedResources.add(src);
                    reject(new Error(`Failed to load script: ${name}`));
                };

                document.head.appendChild(script);
            });
        }

        async loadScriptWithRetry(src, name, maxRetries = CONFIG.retries.maxAttempts) {
            const retryKey = src;
            let attempts = this.retryAttempts.get(retryKey) || 0;

            try {
                await this.loadScript(src, name);
                this.retryAttempts.delete(retryKey);
                return true;
            } catch (error) {
                attempts++;
                this.retryAttempts.set(retryKey, attempts);

                if (attempts < maxRetries) {
                    const delay = CONFIG.retries.delay * Math.pow(2, attempts - 1);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return this.loadScriptWithRetry(src, name, maxRetries);
                } else {
                    console.error(`Failed to load ${name} after ${maxRetries} attempts:`, error);
                    return false;
                }
            }
        }

        async waitForDependency(condition, timeout = 5000, checkInterval = 100) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const check = () => {
                    if (condition()) {
                        resolve();
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error('Dependency timeout'));
                    } else {
                        setTimeout(check, checkInterval);
                    }
                };
                
                check();
            });
        }

        async loadCoreScripts() {
            const scripts = [
                { src: 'utils/mathUtils.js', name: 'Math Utils' },
                { src: 'utils/config.js', name: 'Configuration' }
            ];

            const totalScripts = scripts.length;
            const progressPerScript = CONFIG.progressSteps.SCRIPTS / totalScripts;

            for (const script of scripts) {
                try {
                    await this.loadScriptWithRetry(script.src, script.name);
                    this.updateProgress(progressPerScript, 'Loading Core Scripts', script.name);
                } catch (error) {
                    console.warn(`Non-critical script failed: ${script.name}`, error);
                    this.updateProgress(progressPerScript, 'Loading Core Scripts', `${script.name} (skipped)`);
                }
            }
            
            // Skip remaining progress for disabled scripts
            this.updateProgress(CONFIG.progressSteps.SCRIPTS - (progressPerScript * totalScripts), 'Loading Core Scripts', 'Essential scripts loaded');
        }

        async initializeWebSockets() {
            this.updateProgress(0, 'Initializing WebSockets', 'Setting up connections...');

            try {
                // Check if WebSocketManager is available
                if (window.WebSocketManager) {
                    // Initialize WebSocket managers with connection pooling
                    const connections = [
                        { name: 'Bitstamp', url: 'wss://ws.bitstamp.net' },
                        { name: 'Bybit', url: 'wss://stream.bybit.com/v5/public/linear' }
                    ];

                    const progressPerConnection = CONFIG.progressSteps.WEBSOCKETS / connections.length;

                    for (const conn of connections) {
                        try {
                            if (!window[`${conn.name.toLowerCase()}WsManager`]) {
                                window[`${conn.name.toLowerCase()}WsManager`] = new WebSocketManager(
                                    conn.url, 
                                    conn.name.toLowerCase(),
                                    { 
                                        reconnectDelay: 2000,
                                        maxReconnectAttempts: 5,
                                        connectionPooling: true
                                    }
                                );
                            }
                            this.updateProgress(progressPerConnection, 'Initializing WebSockets', `${conn.name} connected`);
                        } catch (error) {
                            console.warn(`WebSocket connection failed: ${conn.name}`, error);
                            this.updateProgress(progressPerConnection, 'Initializing WebSockets', `${conn.name} (retry pending)`);
                        }
                    }
                } else {
                    console.warn('WebSocketManager not available, skipping WebSocket initialization');
                    this.updateProgress(CONFIG.progressSteps.WEBSOCKETS, 'WebSockets', 'WebSocketManager not available');
                }
            } catch (error) {
                console.error('WebSocket initialization failed:', error);
                this.updateProgress(CONFIG.progressSteps.WEBSOCKETS, 'WebSockets', 'Partial initialization');
            }
        }

        async initializeCharts() {
            this.updateProgress(0, 'Initializing Charts', 'Loading chart library...');

            try {
                // Check if LightweightCharts is available
                if (window.LightweightCharts) {
                    this.updateProgress(10, 'Initializing Charts', 'Chart library loaded');

                    // Pre-create chart containers and states
                    const pairs = ['BTC', 'ETH', 'LTC', 'SOL'];
                    const progressPerPair = (CONFIG.progressSteps.CHARTS - 10) / pairs.length;

                    // Initialize chart states if not already present
                    if (!window.chartStates) {
                        window.chartStates = new Map();
                    }

                    for (const pair of pairs) {
                        try {
                            // Pre-initialize chart state without full chart creation
                            if (!window.chartStates.has(pair)) {
                                window.chartStates.set(pair, {
                                    initialized: false,
                                    preloaded: true,
                                    lastUpdate: Date.now()
                                });
                            }
                            this.updateProgress(progressPerPair, 'Initializing Charts', `${pair} prepared`);
                        } catch (error) {
                            console.warn(`Chart pre-initialization failed for ${pair}:`, error);
                            this.updateProgress(progressPerPair, 'Initializing Charts', `${pair} (skipped)`);
                        }
                    }
                } else {
                    console.warn('LightweightCharts not available, skipping chart initialization');
                    this.updateProgress(CONFIG.progressSteps.CHARTS, 'Charts', 'Chart library not available');
                }
            } catch (error) {
                console.error('Charts initialization failed:', error);
                this.updateProgress(CONFIG.progressSteps.CHARTS, 'Charts', 'Fallback mode');
            }
        }

        async loadIndicators() {
            this.updateProgress(0, 'Loading Indicators', 'Preparing data stores...');

            const indicators = [
                'indicators/data/cvdDataStore.js',
                'indicators/data/perpCvdDataStore.js', 
                'indicators/data/perpImbalanceDataStore.js',
                'indicators/cvd.js',
                'indicators/perpcvd.js',
                'indicators/perpImbalance.js',
                'indicators/indicators.js'
            ];

            const progressPerIndicator = CONFIG.progressSteps.INDICATORS / indicators.length;

            for (const indicator of indicators) {
                try {
                    const name = indicator.split('/').pop().replace('.js', '');
                    await this.loadScriptWithRetry(indicator, name);
                    this.updateProgress(progressPerIndicator, 'Loading Indicators', name);
                } catch (error) {
                    console.warn(`Indicator load failed: ${indicator}`, error);
                    this.updateProgress(progressPerIndicator, 'Loading Indicators', `${indicator} (skipped)`);
                }
            }
        }

        async loadProfiles() {
            this.updateProgress(0, 'Loading Profiles', 'Setting up market profiles...');

            const profiles = [
                // 'profiles/profileManager.js' // Removed, now handled by negativeDeltaOiProfile.js
            ];

            const progressPerProfile = CONFIG.progressSteps.PROFILES / profiles.length;

            for (const profile of profiles) {
                try {
                    const name = profile.split('/').pop().replace('.js', '');
                    await this.loadScriptWithRetry(profile, name);
                    this.updateProgress(progressPerProfile, 'Loading Profiles', name);
                } catch (error) {
                    console.warn(`Profile load failed: ${profile}`, error);
                    this.updateProgress(progressPerProfile, 'Loading Profiles', `${profile} (skipped)`);
                }
            }
        }

        async startMainApplication() {
            try {
                // Initialize basic chart states if not present
                if (!window.chartStates) {
                    window.chartStates = new Map();
                    window.currentPair = 'BTC';
                    window.currentActivePair = 'BTC';
                }

                // Initialize main modules if available
                if (window.chartModule && typeof window.chartModule.initialize === 'function') {
                    try {
                        await window.chartModule.initialize();
                    } catch (error) {
                        console.warn('Chart module initialization failed:', error);
                    }
                }

                // Start data flows if available
                if (window.orderBookModule && typeof window.orderBookModule.start === 'function') {
                    try {
                        window.orderBookModule.start();
                    } catch (error) {
                        console.warn('Order book module start failed:', error);
                    }
                }

                // Skip profile initialization to avoid errors
                console.log('Profile initialization skipped to avoid dependency errors');

                return true;
            } catch (error) {
                console.error('Main application start failed:', error);
                return false;
            }
        }

        pauseLoading() {
            this.statusText.textContent = 'Loading paused...';
            this.statusText.classList.add('loading-pulse');
        }

        resumeLoading() {
            this.statusText.classList.remove('loading-pulse');
            this.statusText.textContent = this.currentStep;
        }

        async completeLoading() {
            if (!this.isLoading) return; // Prevent infinite recursion
            
            this.isLoading = false;
            
            // Start main application
            const appStarted = await this.startMainApplication();
            
            if (!appStarted) {
                if (this.statusText) {
                    this.statusText.textContent = 'Some components may be limited';
                    this.statusText.style.color = '#ffaa00';
                }
            }

            // Notify completion callbacks
            this.completionCallbacks.forEach(callback => {
                try {
                    callback(appStarted);
                } catch (error) {
                    console.warn('Completion callback error:', error);
                }
            });

            // Fade out loading screen
            setTimeout(() => {
                if (this.overlay) {
                    this.overlay.style.opacity = '0';
                    setTimeout(() => {
                        if (this.overlay.parentNode) {
                            this.overlay.parentNode.removeChild(this.overlay);
                        }
                    }, CONFIG.animations.fadeOutDuration);
                }
                
                // Hide simple indicators
                if (this.simpleProgressBar) {
                    this.simpleProgressBar.classList.remove('visible');
                    setTimeout(() => {
                        if (this.simpleProgressBar.parentNode) {
                            this.simpleProgressBar.parentNode.removeChild(this.simpleProgressBar);
                        }
                    }, 300);
                }
                if (this.simpleStatusIndicator) {
                    this.simpleStatusIndicator.classList.remove('visible');
                    setTimeout(() => {
                        if (this.simpleStatusIndicator.parentNode) {
                            this.simpleStatusIndicator.parentNode.removeChild(this.simpleStatusIndicator);
                        }
                    }, 300);
                }
                
                // Trigger ready event
                document.dispatchEvent(new CustomEvent('dashboardReady', {
                    detail: { success: appStarted, loadingTime: Date.now() - this.startTime }
                }));
            }, 1000);
        }

        onProgress(callback) {
            this.progressCallbacks.push(callback);
        }

        onComplete(callback) {
            this.completionCallbacks.push(callback);
        }

        async start() {
            this.startTime = Date.now();
            
            try {
                await this.loadCoreScripts();
                await this.initializeWebSockets();
                await this.initializeCharts();
                await this.loadIndicators();
                await this.loadProfiles();
                
                // Complete loading
                this.updateProgress(100, 'Complete', 'Initializing dashboard...');
                
            } catch (error) {
                console.error('Loading failed:', error);
                this.statusText.textContent = 'Loading completed with errors';
                this.statusText.style.color = '#ff6b6b';
                
                // Still attempt to complete
                setTimeout(() => this.completeLoading(), 2000);
            }
        }
    }

    // Create and expose loading manager
    window.LoadingManager = LoadingManager;
    
    // Auto-start if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.loadingManager = new LoadingManager();
            window.loadingManager.start();
        });
    } else {
        window.loadingManager = new LoadingManager();
        window.loadingManager.start();
    }

})();