/**
 * Performance Optimizer
 * Provides advanced performance optimizations for loading, rendering, and interactions
 */

(function() {
    'use strict';

    // window.saveUIStateToCache and window.restoreUIStateFromCache are now defined in statePersistence.js

    window.performGlobalLightCleanup = function() {
        if (window.performanceOptimizer && typeof window.performanceOptimizer.performLightCleanup === 'function') {
            console.log('[performGlobalLightCleanup] Calling window.performanceOptimizer.performLightCleanup()');
            window.performanceOptimizer.performLightCleanup();
        } else {
            console.warn('[performGlobalLightCleanup] window.performanceOptimizer instance or its performLightCleanup method not ready.');
        }
    };

    const CONFIG = {
        resourceHints: {
            preconnect: ['wss://ws.bitstamp.net', 'wss://stream.bybit.com'],
            prefetch: ['utils/mathUtils.js', 'utils/config.js'],
            preload: {
                scripts: ['wsmanager.js', 'modules/charts.js'],
                fonts: [],
                critical: ['https://unpkg.com/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.js']
            }
        },
        bundling: {
            maxConcurrent: 4,
            batchSize: 3,
            priorityThreshold: 1000
        },
        caching: {
            maxAge: 3600000, // 1 hour
            maxSize: 50 * 1024 * 1024, // 50MB
            compression: true
        },
        rendering: {
            targetFPS: 60,
            budgetMS: 16,
            idleThreshold: 100,
            rafThrottle: true,
            lowFpsThreshold: 30
        },
        memory: {
            gcThreshold: 0.8,
            cleanupInterval: 30000,
            maxHeapSize: 100 * 1024 * 1024 // 100MB
        }
    };

    class PerformanceOptimizer {
        constructor() {
            this.resourceCache = new Map();
            this.loadQueue = new Map();
            this.performanceMetrics = new Map();
            this.observers = new Map();
            this.rafCallbacks = new Set();
            this.idleCallbacks = new Set();
            this.memoryPressure = 0;
            this.isOptimizing = false;
            
            this.init();
        }

        // performLightCleanup is an instance method that calls another instance method.
        performLightCleanup = this.performMemoryCleanup;

        init() {
            this.setupResourceHints();
            this.initializePerformanceMonitoring();
            this.setupMemoryManagement();
            this.optimizeEventLoop();
            this.initializeBundler();
            this.setupCaching();
        }

        setupResourceHints() {
            // DNS preconnect for faster WebSocket connections
            CONFIG.resourceHints.preconnect.forEach(origin => {
                const link = document.createElement('link');
                link.rel = 'preconnect';
                link.href = origin;
                document.head.appendChild(link);
            });

            // Prefetch non-critical resources
            CONFIG.resourceHints.prefetch.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = url;
                link.as = 'script';
                document.head.appendChild(link);
            });

            // Preload critical resources
            CONFIG.resourceHints.preload.critical.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = url;
                link.as = 'script';
                link.crossOrigin = 'anonymous';
                document.head.appendChild(link);
            });
        }

        initializePerformanceMonitoring() {
            if ('PerformanceObserver' in window) {
                // Monitor Long Tasks
                try {
                    const longTaskObserver = new PerformanceObserver(list => {
                        list.getEntries().forEach(entry => {
                            this.handleLongTask(entry);
                        });
                    });
                    longTaskObserver.observe({ entryTypes: ['longtask'] });
                    this.observers.set('longtask', longTaskObserver);
                } catch (e) {
                    console.warn('Long task monitoring not supported');
                }

                // Monitor Layout Shifts
                try {
                    const clsObserver = new PerformanceObserver(list => {
                        list.getEntries().forEach(entry => {
                            this.handleLayoutShift(entry);
                        });
                    });
                    clsObserver.observe({ entryTypes: ['layout-shift'] });
                    this.observers.set('layout-shift', clsObserver);
                } catch (e) {
                    console.warn('Layout shift monitoring not supported');
                }

                // Monitor Paint Timing
                try {
                    const paintObserver = new PerformanceObserver(list => {
                        list.getEntries().forEach(entry => {
                            this.handlePaintTiming(entry);
                        });
                    });
                    paintObserver.observe({ entryTypes: ['paint'] });
                    this.observers.set('paint', paintObserver);
                } catch (e) {
                    console.warn('Paint timing monitoring not supported');
                }
            }

            // FPS monitoring
            this.startFPSMonitoring();
        }

        setupMemoryManagement() {
            if ('memory' in performance) {
                setInterval(() => {
                    this.checkMemoryPressure();
                }, CONFIG.memory.cleanupInterval);
            }

            // Visibility change handling is now managed by wsmanager.js

            // Memory pressure detection
            if ('deviceMemory' in navigator) {
                const deviceMemory = navigator.deviceMemory;
                if (deviceMemory <= 4) {
                    this.enableLowMemoryMode();
                }
            }
        }

        optimizeEventLoop() {
            // Batch RAF callbacks
            let rafScheduled = false;
            const originalRAF = window.requestAnimationFrame;
            
            window.requestAnimationFrame = (callback) => {
                this.rafCallbacks.add(callback);
                
                if (!rafScheduled) {
                    rafScheduled = true;
                    originalRAF(() => {
                        rafScheduled = false;
                        this.processRAFCallbacks();
                    });
                }
            };

            // Optimize idle callbacks
            if ('requestIdleCallback' in window) {
                const processIdleWork = (deadline) => {
                    while (deadline.timeRemaining() > 0 && this.idleCallbacks.size > 0) {
                        const callback = this.idleCallbacks.values().next().value;
                        this.idleCallbacks.delete(callback);
                        try {
                            callback(deadline);
                        } catch (error) {
                            console.warn('Idle callback error:', error);
                        }
                    }
                    
                    if (this.idleCallbacks.size > 0) {
                        requestIdleCallback(processIdleWork);
                    }
                };

                this.scheduleIdleWork = (callback) => {
                    this.idleCallbacks.add(callback);
                    if (this.idleCallbacks.size === 1) {
                        requestIdleCallback(processIdleWork);
                    }
                };
            } else {
                this.scheduleIdleWork = (callback) => {
                    setTimeout(callback, 0);
                };
            }
        }

        initializeBundler() {
            this.scriptBundles = new Map([
                ['core', ['utils/mathUtils.js', 'utils/config.js', 'utils/cleanupManager.js']],
                ['websockets', ['wsmanager.js']],
                ['charts', ['modules/charts.js', 'modules/orderbook.js']],
                ['indicators', ['indicators/cvd.js', 'indicators/perpcvd.js', 'indicators/indicators.js']],
                ['profiles', []]
            ]);
        }

        setupCaching() {
            if ('caches' in window) {
                caches.open('crypto-dashboard-v1').then(cache => {
                    this.cache = cache;
                }).catch(console.warn);
            }

            // Memory cache for frequently accessed data
            this.memoryCache = new Map();
            
            // LRU eviction
            setInterval(() => {
                this.evictLRUCache();
            }, CONFIG.caching.maxAge / 4);
        }

        processRAFCallbacks() {
            const startTime = performance.now();
            const callbacks = Array.from(this.rafCallbacks);
            this.rafCallbacks.clear();

            for (const callback of callbacks) {
                if (performance.now() - startTime > CONFIG.rendering.budgetMS) {
                    // Defer remaining callbacks to next frame
                    this.rafCallbacks.add(callback);
                    requestAnimationFrame(() => this.processRAFCallbacks());
                    break;
                }

                try {
                    callback();
                } catch (error) {
                    console.warn('RAF callback error:', error);
                }
            }
        }

        async loadScriptBundle(bundleName, priority = 0) {
            const scripts = this.scriptBundles.get(bundleName);
            if (!scripts) {
                throw new Error(`Bundle not found: ${bundleName}`);
            }

            const cacheKey = `bundle-${bundleName}`;
            if (this.resourceCache.has(cacheKey)) {
                return this.resourceCache.get(cacheKey);
            }

            const loadPromise = this.loadScriptsConcurrently(scripts, priority);
            this.resourceCache.set(cacheKey, loadPromise);
            
            return loadPromise;
        }

        async loadScriptsConcurrently(scripts, priority = 0) {
            const semaphore = new Semaphore(CONFIG.bundling.maxConcurrent);
            
            const loadTasks = scripts.map(async (script) => {
                await semaphore.acquire();
                try {
                    return await this.loadSingleScript(script, priority);
                } finally {
                    semaphore.release();
                }
            });

            return Promise.allSettled(loadTasks);
        }

        async loadSingleScript(src, priority = 0) {
            return new Promise((resolve, reject) => {
                // Check cache first
                if (this.memoryCache.has(src)) {
                    const cached = this.memoryCache.get(src);
                    if (Date.now() - cached.timestamp < CONFIG.caching.maxAge) {
                        resolve(cached.data);
                        return;
                    }
                }

                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                
                if (priority > CONFIG.bundling.priorityThreshold) {
                    script.setAttribute('importance', 'high');
                }

                const timeout = setTimeout(() => {
                    reject(new Error(`Script load timeout: ${src}`));
                }, 10000);

                script.onload = () => {
                    clearTimeout(timeout);
                    this.memoryCache.set(src, {
                        data: true,
                        timestamp: Date.now()
                    });
                    resolve(true);
                };

                script.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error(`Failed to load: ${src}`));
                };

                document.head.appendChild(script);
            });
        }

        startFPSMonitoring() {
            let lastTime = performance.now();
            let frames = 0;
            
            const measureFPS = () => {
                frames++;
                const currentTime = performance.now();
                
                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                    this.performanceMetrics.set('fps', fps);
                    
                    if (fps < CONFIG.rendering.targetFPS * 0.5) {
                        this.handleLowFPS(fps);
                    }
                    
                    frames = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(measureFPS);
            };
            
            requestAnimationFrame(measureFPS);
        }

        handleLongTask(entry) {
            console.warn(`Long task detected: ${entry.duration}ms`);
            this.performanceMetrics.set('longTaskCount', 
                (this.performanceMetrics.get('longTaskCount') || 0) + 1);
            
            if (entry.duration > 100) {
                this.enablePerformanceMode();
            }
        }

        handleLayoutShift(entry) {
            if (!entry.hadRecentInput) {
                const cls = this.performanceMetrics.get('cls') || 0;
                this.performanceMetrics.set('cls', cls + entry.value);
            }
        }

        handlePaintTiming(entry) {
            this.performanceMetrics.set(entry.name, entry.startTime);
            
            if (entry.name === 'first-contentful-paint' && entry.startTime > 2000) {
                this.optimizeInitialLoad();
            }
        }

        handleLowFPS(fps) {
            console.warn(`Low FPS detected: ${fps}`);
            
            // Reduce visual effects
            document.documentElement.style.setProperty('--animation-duration', '0s');
            
            // Throttle expensive operations
            if (window.chartStates) {
                for (const [pair, state] of window.chartStates) {
                    if (state.chart && state.chart.timeScale) {
                        state.chart.timeScale().setVisibleLogicalRange({
                            from: state.chart.timeScale().getVisibleLogicalRange().from,
                            to: state.chart.timeScale().getVisibleLogicalRange().from + 100
                        });
                    }
                }
            }
        }

        checkMemoryPressure() {
            if (!('memory' in performance)) return;

            const memInfo = performance.memory;
            this.memoryPressure = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;

            if (this.memoryPressure > CONFIG.memory.gcThreshold) {
                this.performMemoryCleanup();
            }
        }

        performMemoryCleanup() {
            console.log('Performing memory cleanup...');

            // Clear old cache entries
            this.evictLRUCache();

            // Cleanup chart states
            if (window.chartStates) {
                const now = Date.now();
                for (const [pair, state] of window.chartStates) {
                    if (now - state.lastUpdate > 300000) { // 5 minutes
                        window.chartStates.delete(pair);
                    }
                }
            }

            // Force garbage collection if available
            if (window.gc) {
                window.gc();
            }
        }

        enableLowMemoryMode() {
            console.log('Enabling low memory mode');
            
            // Reduce cache sizes
            CONFIG.caching.maxSize = 10 * 1024 * 1024; // 10MB
            CONFIG.bundling.maxConcurrent = 2;
            
            // Disable non-essential features
            document.documentElement.classList.add('low-memory-mode');
        }

        enablePerformanceMode() {
            if (this.isOptimizing) return;
            this.isOptimizing = true;

            console.log('Enabling performance mode');

            // Reduce update frequencies
            if (window.CONFIG) {
                window.CONFIG.ui.throttleDelay = 200;
                window.CONFIG.ui.resizeThrottleDelay = 300;
            }

            // Simplify animations
            const style = document.createElement('style');
            style.textContent = `
                .performance-mode * {
                    animation-duration: 0.1s !important;
                    transition-duration: 0.1s !important;
                }
            `;
            document.head.appendChild(style);
            document.body.classList.add('performance-mode');

            setTimeout(() => {
                this.isOptimizing = false;
                document.body.classList.remove('performance-mode');
            }, 10000);
        }

        optimizeInitialLoad() {
            // Defer non-critical script loading
            this.scheduleIdleWork(() => {
                this.loadScriptBundle('profiles', 0);
            });

            // Preload next likely resources
            this.scheduleIdleWork(() => {
                const pairs = ['ETH', 'LTC', 'SOL'];
                pairs.forEach(pair => {
                    if (window.chartSwitcher) {
                        window.chartSwitcher.preloadPair(pair);
                    }
                });
            });
        }

        evictLRUCache() {
            if (this.memoryCache.size === 0) return;

            const entries = Array.from(this.memoryCache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

            const toEvict = Math.floor(entries.length * 0.3);
            for (let i = 0; i < toEvict; i++) {
                this.memoryCache.delete(entries[i][0]);
            }
        }

        // Public API
        getMetrics() {
            return Object.fromEntries(this.performanceMetrics);
        }

        getMemoryUsage() {
            if ('memory' in performance) {
                return {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    pressure: this.memoryPressure
                };
            }
            return null;
        }

        async preloadBundle(bundleName) {
            return this.loadScriptBundle(bundleName, 0);
        }

        scheduleWork(callback, priority = 'idle') {
            if (priority === 'immediate') {
                requestAnimationFrame(callback);
            } else {
                this.scheduleIdleWork(callback);
            }
        }

        cleanup() {
            this.observers.forEach(observer => observer.disconnect());
            this.observers.clear();
            this.resourceCache.clear();
            this.memoryCache.clear();
            this.rafCallbacks.clear();
            this.idleCallbacks.clear();
        }
    }

    // Semaphore for concurrent loading
    class Semaphore {
        constructor(count) {
            this.count = count;
            this.waiting = [];
        }

        async acquire() {
            if (this.count > 0) {
                this.count--;
                return;
            }

            return new Promise(resolve => {
                this.waiting.push(resolve);
            });
        }

        release() {
            if (this.waiting.length > 0) {
                const resolve = this.waiting.shift();
                resolve();
            } else {
                this.count++;
            }
        }
    }

    // Initialize performance optimizer
    window.PerformanceOptimizer = PerformanceOptimizer;
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.performanceOptimizer = new PerformanceOptimizer();
            console.log('[PerformanceOptimizer] Instance created on DOMContentLoaded. Dispatching performanceOptimizerReady event.');
            document.dispatchEvent(new CustomEvent('performanceOptimizerReady'));
        });
    } else {
        window.performanceOptimizer = new PerformanceOptimizer();
        console.log('[PerformanceOptimizer] Instance created immediately. Dispatching performanceOptimizerReady event.');
        document.dispatchEvent(new CustomEvent('performanceOptimizerReady'));
    }

    // Register cleanup
    if (window.CleanupManager?.registerCleanup) {
        window.CleanupManager.registerCleanup(() => {
            if (window.performanceOptimizer) {
                window.performanceOptimizer.cleanup();
                window.performanceOptimizer = null;
            }
        });
    }

    // performGlobalLightCleanup is already defined at the top of the IIFE.

})();