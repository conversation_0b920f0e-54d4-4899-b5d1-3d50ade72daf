// commonUtils.js - Centralized common utility functions

(function() {
    'use strict';

    const commonUtils = {
        // Throttle function - ensures function is called at most once per specified period
        throttle: function(func, limit) {
            let inThrottle, lastFunc, lastRan;
            const wrapper = function() {
                const context = this;
                const args = arguments;
                if (!inThrottle) {
                    func.apply(context, args);
                    lastRan = Date.now();
                    inThrottle = true;
                } else {
                    clearTimeout(lastFunc);
                    lastFunc = setTimeout(() => {
                        if (Date.now() - lastRan >= limit) {
                            func.apply(context, args);
                            lastRan = Date.now();
                        }
                    }, limit - (Date.now() - lastRan));
                }
            };
            
            wrapper.cancel = function() {
                clearTimeout(lastFunc);
                inThrottle = false;
            };

            // Register cleanup for memory management
            if (window.CleanupManager && window.CleanupManager.registerCleanup) {
                window.CleanupManager.registerCleanup(() => {
                    if (lastFunc) clearTimeout(lastFunc);
                });
            }

            return wrapper;
        },

        // Debounce function - delays function execution until after specified wait period
        debounce: function(func, wait, immediate = false) {
            let timeout;
            const debounced = function() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };

            debounced.cancel = function() {
                clearTimeout(timeout);
                timeout = null;
            };

            // Register cleanup for memory management
            if (window.CleanupManager && window.CleanupManager.registerCleanup) {
                window.CleanupManager.registerCleanup(() => {
                    if (timeout) clearTimeout(timeout);
                });
            }

            return debounced;
        },

        // Simple throttle for cases where we just need basic rate limiting
        simpleThrottle: function(fn, limit) {
            let timeout;
            return function(...args) {
                if (!timeout) {
                    timeout = setTimeout(() => {
                        fn(...args);
                        timeout = null;
                    }, limit);
                }
            };
        },

        // Format large numbers for display
        formatLargeNumber: function(value) {
            if (value === null || value === undefined || !isFinite(value)) return '0';
            const absValue = Math.abs(value);
            const sign = value < 0 ? '-' : '';
            
            if (absValue >= 1e9) {
                return sign + (absValue / 1e9).toFixed(1) + 'B';
            } else if (absValue >= 1e6) {
                return sign + (absValue / 1e6).toFixed(1) + 'M';
            } else if (absValue >= 1e3) {
                return sign + (absValue / 1e3).toFixed(1) + 'K';
            } else {
                return sign + absValue.toFixed(0);
            }
        },

        // Format dollar values consistently
        formatDollarValue: function(value) {
            if (value === null || value === undefined || !isFinite(value)) return '$0';
            return '$' + this.formatLargeNumber(value);
        },

        // Deep clone object (simple implementation)
        deepClone: function(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const cloned = {};
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        cloned[key] = this.deepClone(obj[key]);
                    }
                }
                return cloned;
            }
            return obj;
        },

        // Safe JSON parse with fallback
        safeJsonParse: function(str, fallback = null) {
            try {
                return JSON.parse(str);
            } catch (e) {
                return fallback;
            }
        },

        // Get nested property safely
        getNestedProperty: function(obj, path, defaultValue = undefined) {
            const keys = path.split('.');
            let current = obj;
            for (const key of keys) {
                if (current === null || current === undefined || !(key in current)) {
                    return defaultValue;
                }
                current = current[key];
            }
            return current;
        },

        // Set nested property safely
        setNestedProperty: function(obj, path, value) {
            const keys = path.split('.');
            let current = obj;
            for (let i = 0; i < keys.length - 1; i++) {
                const key = keys[i];
                if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
                    current[key] = {};
                }
                current = current[key];
            }
            current[keys[keys.length - 1]] = value;
        },

        // Generate unique ID
        generateId: function(prefix = 'id') {
            return prefix + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        // Check if value is numeric
        isNumeric: function(value) {
            return !isNaN(parseFloat(value)) && isFinite(value);
        },

        // Clamp value between min and max
        clamp: function(value, min, max) {
            return Math.max(min, Math.min(max, value));
        },

        // Round to specified decimal places
        roundToDecimals: function(value, decimals = 2) {
            const factor = Math.pow(10, decimals);
            return Math.round(value * factor) / factor;
        },

        // Check if element is visible in viewport
        isElementVisible: function(element) {
            if (!element || !element.getBoundingClientRect) return false;
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        },

        // Get timestamp in various formats
        timestamp: {
            now: () => Date.now(),
            seconds: () => Math.floor(Date.now() / 1000),
            iso: () => new Date().toISOString(),
            formatted: (date = new Date()) => date.toLocaleString()
        }
    };

    // Expose to global scope
    if (typeof window !== 'undefined') {
        window.commonUtils = commonUtils;
        
        // For backward compatibility, also expose individual functions
        if (!window.utils) window.utils = {};
        window.utils.throttle = commonUtils.throttle;
        window.utils.debounce = commonUtils.debounce;
        window.utils.formatLargeNumber = commonUtils.formatLargeNumber;
        window.utils.formatDollarValue = commonUtils.formatDollarValue;
    }

    // For Node.js environments
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = commonUtils;
    }
})();