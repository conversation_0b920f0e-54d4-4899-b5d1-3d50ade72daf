(function(window) {
    /**
     * Utility to check if a cell is filled (not empty or null)
     * @param {*} cell
     * @returns {boolean}
     */
    function filledCell(cell) {
        return cell !== '' && cell != null;
    }

    /**
     * Load file data from a File object (async, for user uploads)
     * @param {File} file
     * @returns {Promise<string>} Resolves to CSV string
     */
    function loadFileDataFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const workbook = XLSX.read(reader.result, { type: 'array' });
                    const sheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1, blankrows: false, defval: '' });
                    const filteredData = jsonData.filter(row => row.some(filledCell));
                    const headerRowIndex = filteredData.findIndex((row, index) =>
                        row.filter(filledCell).length >= (filteredData[index + 1]?.filter(filledCell).length || 0)
                    ) || 0;
                    const csv = XLSX.utils.sheet_to_csv(XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)));
                    resolve(csv);
                } catch (error) {
                    console.error('Error loading XLSX file:', error);
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Load file data from a filename (sync, for preloaded or global data)
     * @param {string} filename
     * @param {boolean} gk_isXlsx
     * @param {Object} gk_xlsxFileLookup
     * @param {Object} gk_fileData
     * @returns {string} CSV string or raw data
     */
    function loadFileDataFromName(filename, gk_isXlsx, gk_xlsxFileLookup, gk_fileData) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                var filteredData = jsonData.filter(row => row.some(filledCell));
                var headerRowIndex = filteredData.findIndex((row, index) =>
                    row.filter(filledCell).length >= (filteredData[index + 1]?.filter(filledCell).length || 0)
                );
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                    headerRowIndex = 0;
                }
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex));
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
    }

    // Expose the API
    window.fileLoader = {
        loadFileDataFromFile,
        loadFileDataFromName
    };
})(window);